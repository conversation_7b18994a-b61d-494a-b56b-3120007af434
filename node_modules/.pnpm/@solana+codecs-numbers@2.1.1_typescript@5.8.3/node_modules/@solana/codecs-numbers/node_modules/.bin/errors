#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/Desktop/testje/node_modules/.pnpm/@solana+errors@2.1.1_typescript@5.8.3/node_modules/@solana/errors/bin/node_modules:/Users/<USER>/Desktop/testje/node_modules/.pnpm/@solana+errors@2.1.1_typescript@5.8.3/node_modules/@solana/errors/node_modules:/Users/<USER>/Desktop/testje/node_modules/.pnpm/@solana+errors@2.1.1_typescript@5.8.3/node_modules/@solana/node_modules:/Users/<USER>/Desktop/testje/node_modules/.pnpm/@solana+errors@2.1.1_typescript@5.8.3/node_modules:/Users/<USER>/Desktop/testje/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/Desktop/testje/node_modules/.pnpm/@solana+errors@2.1.1_typescript@5.8.3/node_modules/@solana/errors/bin/node_modules:/Users/<USER>/Desktop/testje/node_modules/.pnpm/@solana+errors@2.1.1_typescript@5.8.3/node_modules/@solana/errors/node_modules:/Users/<USER>/Desktop/testje/node_modules/.pnpm/@solana+errors@2.1.1_typescript@5.8.3/node_modules/@solana/node_modules:/Users/<USER>/Desktop/testje/node_modules/.pnpm/@solana+errors@2.1.1_typescript@5.8.3/node_modules:/Users/<USER>/Desktop/testje/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../../@solana+errors@2.1.1_typescript@5.8.3/node_modules/@solana/errors/bin/cli.mjs" "$@"
else
  exec node  "$basedir/../../../../../../@solana+errors@2.1.1_typescript@5.8.3/node_modules/@solana/errors/bin/cli.mjs" "$@"
fi
