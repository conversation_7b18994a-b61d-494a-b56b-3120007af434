{"version": 3, "file": "index.d.ts", "sourceRoot": "", "sources": ["../../../src/coder/index.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,QAAQ,EAAE,UAAU,EAAE,MAAM,WAAW,CAAC;AACjD,OAAO,EAAE,KAAK,EAAE,MAAM,qBAAqB,CAAC;AAE5C,cAAc,kBAAkB,CAAC;AACjC,cAAc,mBAAmB,CAAC;AAElC;;GAEG;AACH,MAAM,WAAW,KAAK,CAAC,CAAC,SAAS,MAAM,GAAG,MAAM,EAAE,CAAC,SAAS,MAAM,GAAG,MAAM;IACzE;;OAEG;IACH,QAAQ,CAAC,WAAW,EAAE,gBAAgB,CAAC;IAEvC;;OAEG;IACH,QAAQ,CAAC,QAAQ,EAAE,aAAa,CAAC,CAAC,CAAC,CAAC;IAEpC;;OAEG;IACH,QAAQ,CAAC,KAAK,EAAE,UAAU,CAAC;IAE3B;;OAEG;IACH,QAAQ,CAAC,MAAM,EAAE,UAAU,CAAC;IAE5B;;OAEG;IACH,QAAQ,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC;CAC/B;AAED,MAAM,WAAW,UAAU;IACzB,MAAM,CAAC,CAAC,GAAG,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;IAC3D,MAAM,CAAC,CAAC,GAAG,GAAG,EAAE,EAAE,EAAE,MAAM,GAAG,CAAC,CAAC;CAChC;AAED,MAAM,WAAW,aAAa,CAAC,CAAC,SAAS,MAAM,GAAG,MAAM;IACtD,MAAM,CAAC,CAAC,GAAG,GAAG,EAAE,WAAW,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;IAC7D,MAAM,CAAC,CAAC,GAAG,GAAG,EAAE,WAAW,EAAE,CAAC,EAAE,EAAE,EAAE,MAAM,GAAG,CAAC,CAAC;IAC/C,eAAe,CAAC,CAAC,GAAG,GAAG,EAAE,WAAW,EAAE,CAAC,EAAE,EAAE,EAAE,MAAM,GAAG,CAAC,CAAC;IACxD,MAAM,CAAC,WAAW,EAAE,CAAC,EAAE,UAAU,CAAC,EAAE,MAAM,GAAG,GAAG,CAAC;IACjD,IAAI,CAAC,UAAU,EAAE,UAAU,GAAG,MAAM,CAAC;CACtC;AAED,MAAM,WAAW,gBAAgB;IAC/B,MAAM,CAAC,MAAM,EAAE,MAAM,EAAE,EAAE,EAAE,GAAG,GAAG,MAAM,CAAC;IACxC,WAAW,CAAC,MAAM,EAAE,MAAM,EAAE,EAAE,EAAE,GAAG,GAAG,MAAM,CAAC;CAC9C;AAED,MAAM,WAAW,UAAU;IACzB,MAAM,CAAC,CAAC,SAAS,QAAQ,GAAG,QAAQ,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,EAC9D,GAAG,EAAE,MAAM,GACV,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC;CACvB;AAED,MAAM,WAAW,UAAU,CAAC,CAAC,SAAS,MAAM,GAAG,MAAM;IACnD,MAAM,CAAC,CAAC,GAAG,GAAG,EAAE,QAAQ,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,GAAG,MAAM,CAAC;IAC9C,MAAM,CAAC,CAAC,GAAG,GAAG,EAAE,QAAQ,EAAE,CAAC,EAAE,QAAQ,EAAE,MAAM,GAAG,CAAC,CAAC;CACnD"}