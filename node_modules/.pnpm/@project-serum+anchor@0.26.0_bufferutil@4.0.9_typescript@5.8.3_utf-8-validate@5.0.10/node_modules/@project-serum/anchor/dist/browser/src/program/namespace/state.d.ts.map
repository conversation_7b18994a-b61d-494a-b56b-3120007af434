{"version": 3, "file": "state.d.ts", "sourceRoot": "", "sources": ["../../../../src/program/namespace/state.ts"], "names": [], "mappings": "AAAA,OAAO,YAAY,MAAM,eAAe,CAAC;AAEzC,OAAO,EACL,SAAS,EAET,UAAU,EAEX,MAAM,iBAAiB,CAAC;AACzB,OAAO,QAAyB,MAAM,mBAAmB,CAAC;AAC1D,OAAO,EAAE,GAAG,EAAE,cAAc,EAAkB,UAAU,EAAE,MAAM,cAAc,CAAC;AAC/E,OAAO,EAAc,KAAK,EAAsB,MAAM,sBAAsB,CAAC;AAC7E,OAAO,EACL,YAAY,EACZ,oBAAoB,EACpB,oBAAoB,EACrB,MAAM,YAAY,CAAC;AAUpB,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,MAAM,YAAY,CAAC;AAE/C,MAAM,CAAC,OAAO,OAAO,YAAY;WACjB,KAAK,CAAC,GAAG,SAAS,GAAG,EACjC,GAAG,EAAE,GAAG,EACR,KAAK,EAAE,KAAK,EACZ,SAAS,EAAE,SAAS,EACpB,QAAQ,CAAC,EAAE,QAAQ,GAClB,WAAW,CAAC,GAAG,CAAC,GAAG,SAAS;CAMhC;AAED,KAAK,eAAe,CAAC,GAAG,SAAS,GAAG,IAAI,GAAG,CAAC,OAAO,CAAC,SAAS,SAAS,GAClE,cAAc,EAAE,GAChB,WAAW,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;AAEzC;;;;GAIG;AACH,qBAAa,WAAW,CAAC,GAAG,SAAS,GAAG;IAgCpC;;OAEG;aACa,QAAQ,EAAE,QAAQ;IAClC;;OAEG;aACa,KAAK,EAAE,KAAK;IAtC9B;;OAEG;IACH,QAAQ,CAAC,GAAG,EAAE,YAAY,CAAC,GAAG,EAAE,eAAe,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;IAE9D;;OAEG;IACH,QAAQ,CAAC,WAAW,EAAE,oBAAoB,CAAC,GAAG,EAAE,eAAe,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;IAE9E;;OAEG;IACH,QAAQ,CAAC,WAAW,EAAE,oBAAoB,CAAC,GAAG,EAAE,eAAe,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;IAE9E;;OAEG;IACH,IAAI,SAAS,IAAI,SAAS,CAEzB;IACD,OAAO,CAAC,UAAU,CAAY;IAE9B,OAAO,CAAC,QAAQ,CAAY;IAC5B,OAAO,CAAC,MAAM,CAAQ;IACtB,OAAO,CAAC,IAAI,CAAM;IAClB,OAAO,CAAC,IAAI,CAAsB;gBAGhC,GAAG,EAAE,GAAG,EACR,SAAS,EAAE,SAAS;IACpB;;OAEG;IACa,QAAQ,GAAE,QAAwB;IAClD;;OAEG;IACa,KAAK,GAAE,KAA2B;IAiEpD;;OAEG;IACG,KAAK,IAAI,OAAO,CACpB,OAAO,CACL,GAAG,CAAC,OAAO,CAAC,SAAS,SAAS,GAC1B,UAAU,GACV,WAAW,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC,EACvC,QAAQ,CAAC,GAAG,CAAC,CACd,CACF;IAkBD;;OAEG;IACH,OAAO,IAAI,SAAS;IAIpB;;;OAGG;IACH,SAAS,CAAC,UAAU,CAAC,EAAE,UAAU,GAAG,YAAY;IAuBhD;;OAEG;IACH,WAAW;CAUZ"}