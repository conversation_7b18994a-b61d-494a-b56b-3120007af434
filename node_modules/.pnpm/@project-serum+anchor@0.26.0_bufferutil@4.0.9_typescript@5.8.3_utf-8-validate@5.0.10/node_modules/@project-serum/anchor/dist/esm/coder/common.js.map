{"version": 3, "file": "common.js", "sourceRoot": "", "sources": ["../../../src/coder/common.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,QAAQ,EAAE,MAAM,aAAa,CAAC;AAEvC,MAAM,UAAU,WAAW,CAAC,GAAQ,EAAE,UAAsB;IAC1D,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,KAAK,MAAM,EAAE;QACnC,IAAI,YAAY,GAAG,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAC7C,CAAC,OAAuB,EAAE,EAAE;YAC1B,IAAI,OAAO,CAAC,MAAM,KAAK,SAAS,EAAE;gBAChC,OAAO,CAAC,CAAC;aACV;YACD,OAAO,OAAO,CAAC,MAAM;iBAClB,GAAG,CAAC,CAAC,CAAqB,EAAE,EAAE;gBAC7B,IAAI,CAAC,CAAC,OAAO,CAAC,KAAK,QAAQ,IAAI,MAAM,IAAI,CAAC,CAAC,EAAE;oBAC3C,MAAM,IAAI,KAAK,CAAC,0CAA0C,CAAC,CAAC;iBAC7D;gBACD,OAAO,QAAQ,CAAC,GAAG,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC;YAC/B,CAAC,CAAC;iBACD,MAAM,CAAC,CAAC,CAAS,EAAE,CAAS,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QAC7C,CAAC,CACF,CAAC;QACF,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC;KACtC;IACD,IAAI,UAAU,CAAC,IAAI,CAAC,MAAM,KAAK,SAAS,EAAE;QACxC,OAAO,CAAC,CAAC;KACV;IACD,OAAO,UAAU,CAAC,IAAI,CAAC,MAAM;SAC1B,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC;SACjC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;AAChC,CAAC;AAED,gFAAgF;AAChF,qDAAqD;AACrD,SAAS,QAAQ,CAAC,GAAQ,EAAE,EAAW;;IACrC,QAAQ,EAAE,EAAE;QACV,KAAK,MAAM;YACT,OAAO,CAAC,CAAC;QACX,KAAK,IAAI;YACP,OAAO,CAAC,CAAC;QACX,KAAK,IAAI;YACP,OAAO,CAAC,CAAC;QACX,KAAK,KAAK;YACR,OAAO,CAAC,CAAC;QACX,KAAK,KAAK;YACR,OAAO,CAAC,CAAC;QACX,KAAK,KAAK;YACR,OAAO,CAAC,CAAC;QACX,KAAK,KAAK;YACR,OAAO,CAAC,CAAC;QACX,KAAK,KAAK;YACR,OAAO,CAAC,CAAC;QACX,KAAK,KAAK;YACR,OAAO,CAAC,CAAC;QACX,KAAK,KAAK;YACR,OAAO,CAAC,CAAC;QACX,KAAK,KAAK;YACR,OAAO,CAAC,CAAC;QACX,KAAK,MAAM;YACT,OAAO,EAAE,CAAC;QACZ,KAAK,MAAM;YACT,OAAO,EAAE,CAAC;QACZ,KAAK,MAAM;YACT,OAAO,EAAE,CAAC;QACZ,KAAK,MAAM;YACT,OAAO,EAAE,CAAC;QACZ,KAAK,OAAO;YACV,OAAO,CAAC,CAAC;QACX,KAAK,QAAQ;YACX,OAAO,CAAC,CAAC;QACX,KAAK,WAAW;YACd,OAAO,EAAE,CAAC;QACZ;YACE,IAAI,KAAK,IAAI,EAAE,EAAE;gBACf,OAAO,CAAC,CAAC;aACV;YACD,IAAI,QAAQ,IAAI,EAAE,EAAE;gBAClB,OAAO,CAAC,GAAG,QAAQ,CAAC,GAAG,EAAE,EAAE,CAAC,MAAM,CAAC,CAAC;aACrC;YACD,IAAI,SAAS,IAAI,EAAE,EAAE;gBACnB,OAAO,CAAC,GAAG,QAAQ,CAAC,GAAG,EAAE,EAAE,CAAC,OAAO,CAAC,CAAC;aACtC;YACD,IAAI,SAAS,IAAI,EAAE,EAAE;gBACnB,MAAM,QAAQ,GAAG,MAAA,MAAA,GAAG,CAAC,KAAK,0CAAE,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,EAAE,CAAC,OAAO,CAAC,mCAAI,EAAE,CAAC;gBACvE,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE;oBACzB,MAAM,IAAI,QAAQ,CAAC,mBAAmB,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;iBAC7D;gBACD,IAAI,OAAO,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;gBAE1B,OAAO,WAAW,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;aAClC;YACD,IAAI,OAAO,IAAI,EAAE,EAAE;gBACjB,IAAI,OAAO,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;gBAC1B,IAAI,SAAS,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;gBAC5B,OAAO,QAAQ,CAAC,GAAG,EAAE,OAAO,CAAC,GAAG,SAAS,CAAC;aAC3C;YACD,MAAM,IAAI,KAAK,CAAC,gBAAgB,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;KACzD;AACH,CAAC"}