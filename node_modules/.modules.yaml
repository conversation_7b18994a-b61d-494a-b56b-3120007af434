hoistPattern:
  - '*'
hoistedDependencies:
  '@babel/runtime@7.27.6':
    '@babel/runtime': private
  '@coral-xyz/borsh@0.26.0(@solana/web3.js@1.98.2(bufferutil@4.0.9)(typescript@5.8.3)(utf-8-validate@5.0.10))':
    '@coral-xyz/borsh': private
  '@noble/curves@1.9.2':
    '@noble/curves': private
  '@noble/hashes@1.8.0':
    '@noble/hashes': private
  '@solana/buffer-layout@4.0.1':
    '@solana/buffer-layout': private
  '@solana/codecs-core@2.1.1(typescript@5.8.3)':
    '@solana/codecs-core': private
  '@solana/codecs-numbers@2.1.1(typescript@5.8.3)':
    '@solana/codecs-numbers': private
  '@solana/errors@2.1.1(typescript@5.8.3)':
    '@solana/errors': private
  '@swc/helpers@0.5.17':
    '@swc/helpers': private
  '@types/connect@3.4.38':
    '@types/connect': private
  '@types/node@12.20.55':
    '@types/node': private
  '@types/uuid@8.3.4':
    '@types/uuid': private
  '@types/ws@7.4.7':
    '@types/ws': private
  agentkeepalive@4.6.0:
    agentkeepalive: private
  asynckit@0.4.0:
    asynckit: private
  base-x@3.0.11:
    base-x: private
  base64-js@1.5.1:
    base64-js: private
  bn.js@5.2.2:
    bn.js: private
  bs58@4.0.1:
    bs58: private
  buffer-layout@1.2.2:
    buffer-layout: private
  buffer@6.0.3:
    buffer: private
  bufferutil@4.0.9:
    bufferutil: private
  call-bind-apply-helpers@1.0.2:
    call-bind-apply-helpers: private
  camelcase@6.3.0:
    camelcase: private
  chalk@5.4.1:
    chalk: private
  combined-stream@1.0.8:
    combined-stream: private
  commander@2.20.3:
    commander: private
  cross-fetch@3.2.0:
    cross-fetch: private
  crypto-hash@1.3.0:
    crypto-hash: private
  delay@5.0.0:
    delay: private
  delayed-stream@1.0.0:
    delayed-stream: private
  dot-case@3.0.4:
    dot-case: private
  dunder-proto@1.0.1:
    dunder-proto: private
  es-define-property@1.0.1:
    es-define-property: private
  es-errors@1.3.0:
    es-errors: private
  es-object-atoms@1.1.1:
    es-object-atoms: private
  es-set-tostringtag@2.1.0:
    es-set-tostringtag: private
  es6-promise@4.2.8:
    es6-promise: private
  es6-promisify@5.0.0:
    es6-promisify: private
  eventemitter3@4.0.7:
    eventemitter3: private
  eyes@0.1.8:
    eyes: private
  fast-stable-stringify@1.0.0:
    fast-stable-stringify: private
  follow-redirects@1.15.9:
    follow-redirects: private
  form-data@4.0.3:
    form-data: private
  function-bind@1.1.2:
    function-bind: private
  get-intrinsic@1.3.0:
    get-intrinsic: private
  get-proto@1.0.1:
    get-proto: private
  gopd@1.2.0:
    gopd: private
  has-symbols@1.1.0:
    has-symbols: private
  has-tostringtag@1.0.2:
    has-tostringtag: private
  hasown@2.0.2:
    hasown: private
  humanize-ms@1.2.1:
    humanize-ms: private
  ieee754@1.2.1:
    ieee754: private
  isomorphic-ws@4.0.1(ws@7.5.10(bufferutil@4.0.9)(utf-8-validate@5.0.10)):
    isomorphic-ws: private
  jayson@4.2.0(bufferutil@4.0.9)(utf-8-validate@5.0.10):
    jayson: private
  js-sha256@0.9.0:
    js-sha256: private
  json-stringify-safe@5.0.1:
    json-stringify-safe: private
  lower-case@2.0.2:
    lower-case: private
  math-intrinsics@1.1.0:
    math-intrinsics: private
  mime-db@1.52.0:
    mime-db: private
  mime-types@2.1.35:
    mime-types: private
  ms@2.1.3:
    ms: private
  no-case@3.0.4:
    no-case: private
  node-fetch@2.7.0:
    node-fetch: private
  node-gyp-build@4.8.4:
    node-gyp-build: private
  pako@2.1.0:
    pako: private
  proxy-from-env@1.1.0:
    proxy-from-env: private
  rpc-websockets@9.1.1:
    rpc-websockets: private
  safe-buffer@5.2.1:
    safe-buffer: private
  snake-case@3.0.4:
    snake-case: private
  stream-chain@2.2.5:
    stream-chain: private
  stream-json@1.9.1:
    stream-json: private
  superstruct@0.15.5:
    superstruct: private
  text-encoding-utf-8@1.0.2:
    text-encoding-utf-8: private
  toml@3.0.0:
    toml: private
  tr46@0.0.3:
    tr46: private
  tslib@2.8.1:
    tslib: private
  typescript@5.8.3:
    typescript: private
  undici-types@7.8.0:
    undici-types: private
  utf-8-validate@5.0.10:
    utf-8-validate: private
  uuid@8.3.2:
    uuid: private
  webidl-conversions@3.0.1:
    webidl-conversions: private
  whatwg-url@5.0.0:
    whatwg-url: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.10.0
pendingBuilds: []
prunedAt: Tue, 24 Jun 2025 04:31:03 GMT
publicHoistPattern: []
registries:
  '@jsr': https://npm.jsr.io/
  default: https://registry.npmjs.org/
skipped: []
storeDir: /Users/<USER>/Library/pnpm/store/v10
virtualStoreDir: .pnpm
virtualStoreDirMaxLength: 120
