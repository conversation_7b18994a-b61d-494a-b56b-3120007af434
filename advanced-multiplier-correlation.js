import fs from 'fs';
import { Buffer } from 'buffer';
import { createHmac } from 'crypto';
import crypto from 'crypto';

/**
 * Advanced analysis to find hidden correlations between instruction data and multiplier
 */

// Load the analysis data
const analysisData = JSON.parse(fs.readFileSync('./multiplier-prediction-analysis.json', 'utf8'));
const games = analysisData.games;

console.log('🔬 ADVANCED MULTIPLIER CORRELATION ANALYSIS');
console.log('===========================================');

console.log('\n📊 INSTRUCTION DATA SUMMARY:');
console.log('============================');
games.forEach((game, i) => {
  console.log(`Game ${i+1} (${game.gameId}):`);
  console.log(`  Multiplier: ${game.actualMultiplier}`);
  console.log(`  Variable1: ${game.variable1} (0x${game.variable1Hex})`);
  console.log(`  Variable2: ${game.variable2} (0x${game.variable2Hex}) ← Your focus`);
  console.log(`  LastByte: ${game.lastByte} (0x${game.lastByteHex})`);
  console.log(`  H value needed: ${parseInt(game.saltedSeed.slice(0, 13), 16)}`);
});

console.log('\n🎯 HYPOTHESIS TESTING:');
console.log('======================');

// Hypothesis 1: Instruction data is used as seed for PRNG that generates server seed
console.log('\n1️⃣ PRNG SEED HYPOTHESIS:');
games.forEach((game, i) => {
  console.log(`\nGame ${i+1}:`);
  
  // Try using variable2 as seed for different PRNGs
  const seed = game.variable2;
  
  // Linear Congruential Generator
  function lcg(seed, a = 1664525, c = 1013904223, m = Math.pow(2, 32)) {
    return ((a * seed + c) % m);
  }
  
  // Try multiple LCG iterations
  let currentSeed = seed;
  for (let iter = 1; iter <= 5; iter++) {
    currentSeed = lcg(currentSeed);
    const hash = crypto.createHash('sha256').update(currentSeed.toString()).digest('hex');
    
    if (hash.slice(0, 16) === game.serverSeed.slice(0, 16)) {
      console.log(`  🎉 LCG iteration ${iter} matches server seed start!`);
    }
    
    if (hash === game.serverSeed) {
      console.log(`  🎉 LCG iteration ${iter} generates exact server seed!`);
    }
  }
});

// Hypothesis 2: Instruction data encodes the multiplier directly
console.log('\n2️⃣ DIRECT ENCODING HYPOTHESIS:');
games.forEach((game, i) => {
  console.log(`\nGame ${i+1}:`);
  
  const multiplier = game.actualMultiplier;
  const multiplierFixed = Math.round(multiplier * 100); // 3.72 -> 372
  
  // Check if multiplier is encoded in the data
  const allValues = [game.variable1, game.variable2, game.lastByte];
  const allHex = [game.variable1Hex, game.variable2Hex, game.lastByteHex];
  
  // Try different encodings
  const encodings = [
    multiplier * 100,
    multiplier * 1000,
    multiplier * 10000,
    Math.floor(multiplier * 100),
    Math.ceil(multiplier * 100),
    multiplier * 100 + 1000000000, // Offset encoding
    multiplier * 100 ^ 0xFFFFFFFF,  // XOR encoding
  ];
  
  encodings.forEach((encoded, idx) => {
    if (allValues.includes(Math.floor(encoded))) {
      console.log(`  🎯 Encoding ${idx + 1}: Multiplier * ${[100,1000,10000,100,100,'offset','xor'][idx]} = ${encoded} found in instruction data!`);
    }
  });
});

// Hypothesis 3: Instruction data is part of the HMAC input
console.log('\n3️⃣ HMAC INPUT HYPOTHESIS:');
games.forEach((game, i) => {
  console.log(`\nGame ${i+1}:`);
  
  // Try using instruction data as part of HMAC
  const instructionData = game.variablePart;
  
  // Test if instruction data + something = server seed
  const attempts = [
    { name: 'SHA256(variable2)', value: crypto.createHash('sha256').update(game.variable2.toString()).digest('hex') },
    { name: 'SHA256(variablePart)', value: crypto.createHash('sha256').update(instructionData, 'hex').digest('hex') },
    { name: 'SHA256(variable1+variable2)', value: crypto.createHash('sha256').update((game.variable1 + game.variable2).toString()).digest('hex') },
  ];
  
  attempts.forEach(attempt => {
    if (attempt.value === game.serverSeed) {
      console.log(`  🎉 ${attempt.name} generates server seed!`);
    }
    
    if (attempt.value.slice(0, 16) === game.serverSeed.slice(0, 16)) {
      console.log(`  ✅ ${attempt.name} matches server seed prefix!`);
    }
  });
});

// Hypothesis 4: Instruction data determines the h value through transformation
console.log('\n4️⃣ H VALUE TRANSFORMATION HYPOTHESIS:');
games.forEach((game, i) => {
  console.log(`\nGame ${i+1}:`);
  
  const targetH = parseInt(game.saltedSeed.slice(0, 13), 16);
  console.log(`  Target h: ${targetH}`);
  
  // Try various transformations of instruction data to get h
  const transformations = [
    { name: 'variable2 << 32', value: BigInt(game.variable2) << 32n },
    { name: 'variable1 << 32', value: BigInt(game.variable1) << 32n },
    { name: '(variable1 << 32) | variable2', value: (BigInt(game.variable1) << 32n) | BigInt(game.variable2) },
    { name: 'variable2 * 1000000000', value: BigInt(game.variable2) * 1000000000n },
    { name: 'variable1 * variable2', value: BigInt(game.variable1) * BigInt(game.variable2) },
  ];
  
  transformations.forEach(transform => {
    const value = Number(transform.value % (2n ** 52n)); // Keep within 52-bit range
    if (Math.abs(value - targetH) < 1000000) { // Close match
      console.log(`  🎯 ${transform.name} = ${value} (close to target ${targetH})`);
    }
    if (value === targetH) {
      console.log(`  🎉 EXACT MATCH! ${transform.name} = ${value}`);
    }
  });
});

// Hypothesis 5: Reverse engineering - what would generate the observed h values?
console.log('\n5️⃣ REVERSE ENGINEERING ANALYSIS:');
games.forEach((game, i) => {
  console.log(`\nGame ${i+1}:`);
  
  const targetH = parseInt(game.saltedSeed.slice(0, 13), 16);
  const multiplier = game.actualMultiplier;
  
  // If we know the multiplier, what h value should we have?
  const e = Math.pow(2, 52);
  const expectedH = (100 * e - multiplier * 100 * e) / (multiplier * 100 - 100);
  
  console.log(`  Actual h: ${targetH}`);
  console.log(`  Expected h for multiplier ${multiplier}: ${Math.floor(expectedH)}`);
  console.log(`  Difference: ${Math.abs(targetH - expectedH)}`);
  
  // What simple operation on instruction data gives us targetH?
  const ratio1 = targetH / game.variable1;
  const ratio2 = targetH / game.variable2;
  
  console.log(`  targetH / variable1 = ${ratio1}`);
  console.log(`  targetH / variable2 = ${ratio2}`);
  
  // Check if it's a power of 2 relationship
  const log2_1 = Math.log2(ratio1);
  const log2_2 = Math.log2(ratio2);
  
  if (Math.abs(log2_1 - Math.round(log2_1)) < 0.01) {
    console.log(`  🎯 variable1 * 2^${Math.round(log2_1)} ≈ targetH`);
  }
  
  if (Math.abs(log2_2 - Math.round(log2_2)) < 0.01) {
    console.log(`  🎯 variable2 * 2^${Math.round(log2_2)} ≈ targetH`);
  }
});

// Hypothesis 6: Check if instruction data appears anywhere in the HMAC process
console.log('\n6️⃣ HMAC PROCESS ANALYSIS:');
games.forEach((game, i) => {
  console.log(`\nGame ${i+1}:`);
  
  // Check if instruction data appears in server seed, client seed, or salted seed
  const instructionHex = game.variablePart;
  const var2Hex = game.variable2Hex;
  
  console.log(`  Variable part: ${instructionHex}`);
  console.log(`  Variable2: ${var2Hex}`);
  
  if (game.serverSeed.includes(instructionHex)) {
    console.log(`  🎯 Variable part found in server seed!`);
  }
  
  if (game.clientSeed && game.clientSeed.includes(instructionHex)) {
    console.log(`  🎯 Variable part found in client seed!`);
  }
  
  if (game.saltedSeed.includes(instructionHex)) {
    console.log(`  🎯 Variable part found in salted seed!`);
  }
  
  if (game.saltedSeed.includes(var2Hex)) {
    console.log(`  🎯 Variable2 found in salted seed!`);
  }
});

console.log('\n📋 CONCLUSIONS:');
console.log('===============');
console.log('Based on the analysis:');
console.log('1. Instruction data values are much smaller than h values needed');
console.log('2. No direct encoding of multiplier found in instruction data');
console.log('3. No simple transformation generates the required h values');
console.log('4. Instruction data does not appear directly in HMAC inputs/outputs');
console.log('');
console.log('🎯 NEXT STEPS:');
console.log('- Collect more games to find patterns');
console.log('- Look for complex mathematical relationships');
console.log('- Consider that instruction data might influence server-side seed generation');
console.log('- Analyze timing relationships between instruction data and game outcomes');

console.log('\n💡 IMPORTANT INSIGHT:');
console.log('The crash multiplier appears to be determined by:');
console.log('ServerSeed + ClientSeed → HMAC → h value → Multiplier');
console.log('');
console.log('If instruction data influences the outcome, it likely affects:');
console.log('1. Server seed generation (server-side)');
console.log('2. Client seed selection (timing-based)');
console.log('3. Some other part of the process not visible in the algorithm');
