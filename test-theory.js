import fs from 'fs';
import { Buffer } from 'buffer';

/**
 * Test our breakthrough theory on all captured games
 * Theory: LastByte / 40 ≈ Multiplier
 */

console.log('🧪 TESTING MULTIPLIER PREDICTION THEORY');
console.log('=======================================');
console.log('Theory: LastByte / 40 ≈ Crash Multiplier');

// Load all complete game files
const gameFiles = fs.readdirSync('./complete-games').filter(file => file.endsWith('.json'));
console.log(`\n📊 Found ${gameFiles.length} complete games to test`);

const games = gameFiles.map(file => {
  try {
    const gameData = JSON.parse(fs.readFileSync(`./complete-games/${file}`, 'utf8'));
    
    // Extract instruction data
    const createStage = gameData.stages.CreateCrashRound;
    const instruction = createStage.transaction.transaction.message.instructions.find(
      inst => inst.programId === '6YVBrao9DSLVGk7QsXxQVPbs4XyHVtHw9oSTTXw1otEW'
    );
    
    const buffer = Buffer.from(instruction.data, 'base64');
    const data = buffer.slice(8); // Skip discriminator
    
    return {
      file: file,
      gameId: gameData.game_info.custom_id,
      actualMultiplier: gameData.api_snapshots.EndCrashRound?.crashMultiplier,
      serverSeed: gameData.api_snapshots.EndCrashRound?.serverSeed,
      variable1: data.readUInt32LE(4),
      variable2: data.readUInt32LE(8),
      lastByte: data[12],
      blockTime: createStage.transaction.blockTime
    };
  } catch (e) {
    console.log(`❌ Could not load ${file}:`, e.message);
    return null;
  }
}).filter(Boolean);

console.log('\n📋 GAME DATA SUMMARY:');
console.log('=====================');
games.forEach((game, i) => {
  console.log(`${i+1}. ${game.gameId}: Multiplier=${game.actualMultiplier}, LastByte=${game.lastByte}`);
});

console.log('\n🎯 TESTING MAIN THEORY: LastByte / 40');
console.log('=====================================');

let perfectMatches = 0;
let closeMatches = 0;
let totalError = 0;

games.forEach((game, i) => {
  const predicted = game.lastByte / 40;
  const error = Math.abs(predicted - game.actualMultiplier);
  totalError += error;
  
  if (error < 0.1) perfectMatches++;
  if (error < 1.0) closeMatches++;
  
  const status = error < 0.1 ? '🎯 PERFECT' : error < 1.0 ? '✅ CLOSE' : '❌ OFF';
  
  console.log(`Game ${i+1} (${game.gameId}):`);
  console.log(`  LastByte: ${game.lastByte}`);
  console.log(`  Predicted: ${predicted.toFixed(2)}`);
  console.log(`  Actual: ${game.actualMultiplier}`);
  console.log(`  Error: ${error.toFixed(2)} ${status}`);
  console.log('');
});

const avgError = totalError / games.length;
console.log(`📊 RESULTS SUMMARY:`);
console.log(`==================`);
console.log(`Perfect matches (< 0.1 error): ${perfectMatches}/${games.length}`);
console.log(`Close matches (< 1.0 error): ${closeMatches}/${games.length}`);
console.log(`Average error: ${avgError.toFixed(3)}`);
console.log(`Success rate: ${(closeMatches/games.length*100).toFixed(1)}%`);

console.log('\n🔬 TESTING ALTERNATIVE FORMULAS:');
console.log('================================');

const formulas = [
  { name: 'LastByte / 39', calc: (game) => game.lastByte / 39 },
  { name: 'LastByte / 39.5', calc: (game) => game.lastByte / 39.5 },
  { name: 'LastByte / 40.5', calc: (game) => game.lastByte / 40.5 },
  { name: 'LastByte / 41', calc: (game) => game.lastByte / 41 },
  { name: '(LastByte - 5) / 40', calc: (game) => (game.lastByte - 5) / 40 },
  { name: '(LastByte + 5) / 40', calc: (game) => (game.lastByte + 5) / 40 },
  { name: 'LastByte / 38', calc: (game) => game.lastByte / 38 },
  { name: 'LastByte / 42', calc: (game) => game.lastByte / 42 },
  { name: 'sqrt(LastByte * 10)', calc: (game) => Math.sqrt(game.lastByte * 10) },
  { name: 'LastByte^1.1 / 50', calc: (game) => Math.pow(game.lastByte, 1.1) / 50 }
];

formulas.forEach(formula => {
  let formulaError = 0;
  let formulaPerfect = 0;
  let formulaClose = 0;
  
  console.log(`\n${formula.name}:`);
  
  games.forEach(game => {
    const predicted = formula.calc(game);
    const error = Math.abs(predicted - game.actualMultiplier);
    formulaError += error;
    
    if (error < 0.1) formulaPerfect++;
    if (error < 1.0) formulaClose++;
  });
  
  const formulaAvgError = formulaError / games.length;
  console.log(`  Average Error: ${formulaAvgError.toFixed(3)}`);
  console.log(`  Perfect: ${formulaPerfect}/${games.length}, Close: ${formulaClose}/${games.length}`);
  console.log(`  Success Rate: ${(formulaClose/games.length*100).toFixed(1)}%`);
  
  if (formulaAvgError < avgError) {
    console.log(`  🎯 BETTER than main theory!`);
  }
});

console.log('\n🧮 TESTING COMPLEX FORMULAS:');
console.log('============================');

const complexFormulas = [
  {
    name: 'Weighted Sum with LastByte',
    calc: (game) => (game.variable1 * 0.8 + game.variable2 * 0.2) * game.lastByte / Math.pow(10, 11)
  },
  {
    name: 'Modular Arithmetic',
    calc: (game) => ((game.variable1 + game.variable2) % 1000000) / 100000
  },
  {
    name: 'LastByte with Variable Adjustment',
    calc: (game) => (game.lastByte + (game.variable1 % 100) / 1000) / 40
  },
  {
    name: 'Dynamic Divisor',
    calc: (game) => game.lastByte / (40 + (game.variable2 % 10))
  }
];

complexFormulas.forEach(formula => {
  let formulaError = 0;
  let formulaPerfect = 0;
  let formulaClose = 0;
  
  console.log(`\n${formula.name}:`);
  
  games.forEach(game => {
    const predicted = formula.calc(game);
    const error = Math.abs(predicted - game.actualMultiplier);
    formulaError += error;
    
    if (error < 0.1) formulaPerfect++;
    if (error < 1.0) formulaClose++;
  });
  
  const formulaAvgError = formulaError / games.length;
  console.log(`  Average Error: ${formulaAvgError.toFixed(3)}`);
  console.log(`  Perfect: ${formulaPerfect}/${games.length}, Close: ${formulaClose}/${games.length}`);
  console.log(`  Success Rate: ${(formulaClose/games.length*100).toFixed(1)}%`);
});

console.log('\n🔍 ANALYZING OUTLIERS:');
console.log('======================');

// Find games where LastByte/40 doesn't work well
const outliers = games.filter(game => {
  const predicted = game.lastByte / 40;
  const error = Math.abs(predicted - game.actualMultiplier);
  return error > 1.0;
});

if (outliers.length > 0) {
  console.log(`Found ${outliers.length} outlier games:`);
  outliers.forEach(game => {
    const predicted = game.lastByte / 40;
    const error = Math.abs(predicted - game.actualMultiplier);
    console.log(`  ${game.gameId}: LastByte=${game.lastByte}, Predicted=${predicted.toFixed(2)}, Actual=${game.actualMultiplier}, Error=${error.toFixed(2)}`);
    
    // Check if there's a pattern in outliers
    console.log(`    Variable1: ${game.variable1}, Variable2: ${game.variable2}`);
    console.log(`    V1/V2 ratio: ${(game.variable1/game.variable2).toFixed(3)}`);
  });
} else {
  console.log('🎉 No outliers found! Theory works for all games!');
}

console.log('\n📈 CORRELATION ANALYSIS:');
console.log('========================');

// Calculate correlation between LastByte and actual multiplier
const lastBytes = games.map(g => g.lastByte);
const multipliers = games.map(g => g.actualMultiplier);

const meanLastByte = lastBytes.reduce((sum, lb) => sum + lb, 0) / lastBytes.length;
const meanMultiplier = multipliers.reduce((sum, m) => sum + m, 0) / multipliers.length;

const numerator = lastBytes.reduce((sum, lb, i) => sum + (lb - meanLastByte) * (multipliers[i] - meanMultiplier), 0);
const denomLB = Math.sqrt(lastBytes.reduce((sum, lb) => sum + Math.pow(lb - meanLastByte, 2), 0));
const denomMult = Math.sqrt(multipliers.reduce((sum, m) => sum + Math.pow(m - meanMultiplier, 2), 0));

const correlation = numerator / (denomLB * denomMult);

console.log(`LastByte vs Multiplier correlation: ${correlation.toFixed(4)}`);
console.log(`Mean LastByte: ${meanLastByte.toFixed(1)}`);
console.log(`Mean Multiplier: ${meanMultiplier.toFixed(2)}`);

if (correlation > 0.8) {
  console.log('🎯 STRONG positive correlation confirmed!');
} else if (correlation > 0.5) {
  console.log('✅ MODERATE positive correlation found');
} else {
  console.log('❌ Weak correlation - theory needs refinement');
}

console.log('\n📋 FINAL ASSESSMENT:');
console.log('====================');

if (perfectMatches >= games.length * 0.5) {
  console.log('🎉 THEORY CONFIRMED! Majority of games have perfect predictions');
} else if (closeMatches >= games.length * 0.7) {
  console.log('✅ THEORY MOSTLY WORKS! Most games have close predictions');
} else {
  console.log('🔄 THEORY NEEDS REFINEMENT! Mixed results across games');
}

console.log(`\n🎯 BEST FORMULA FOR PREDICTION:`);
console.log(`LastByte / 40 = Predicted Multiplier`);
console.log(`Success rate: ${(closeMatches/games.length*100).toFixed(1)}%`);
console.log(`Average error: ${avgError.toFixed(3)}`);

// Save test results
const testResults = {
  theory: 'LastByte / 40 ≈ Crash Multiplier',
  total_games: games.length,
  perfect_matches: perfectMatches,
  close_matches: closeMatches,
  success_rate: (closeMatches/games.length*100).toFixed(1) + '%',
  average_error: avgError.toFixed(3),
  correlation: correlation.toFixed(4),
  games: games.map(game => ({
    gameId: game.gameId,
    lastByte: game.lastByte,
    predicted: (game.lastByte / 40).toFixed(2),
    actual: game.actualMultiplier,
    error: Math.abs(game.lastByte / 40 - game.actualMultiplier).toFixed(2)
  })),
  outliers: outliers.map(game => ({
    gameId: game.gameId,
    lastByte: game.lastByte,
    predicted: (game.lastByte / 40).toFixed(2),
    actual: game.actualMultiplier,
    error: Math.abs(game.lastByte / 40 - game.actualMultiplier).toFixed(2)
  })),
  analysis_timestamp: new Date().toISOString()
};

fs.writeFileSync('./theory-test-results.json', JSON.stringify(testResults, null, 2));
console.log('\n💾 Test results saved to theory-test-results.json');
