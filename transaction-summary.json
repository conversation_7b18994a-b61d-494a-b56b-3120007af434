{"transaction_info": {"signature": "3Jj7nEKceKCSi8R5j5CuF1eWbLe4S3RxgBnVj5sBekr4ENcEd3iQeAdoMsQn1n48ectKvBqdzHcXLsSm9sp911xm", "block_time": "2025-06-24T05:52:37.000Z", "slot": *********}, "program_info": {"program_id": "6YVBrao9DSLVGk7QsXxQVPbs4XyHVtHw9oSTTXw1otEW", "program_name": "Crash Game Program"}, "instructions": [{"instruction": "CreateCrashRound", "accounts": {"account_1": "rw7igePyZ5r7RmY1nuPU8CdorrHkrMic5sfchbwbjK8", "account_2": "EwsWeAvhBGr5vf5VWhqjy1hC2Z9guyaiLz2ErDy34rUZ"}, "data_analysis": {"discriminator": "d80d78f2448503ab", "data_bytes": 13, "raw_data_hex": "6a54a0fd2d04d66135ead926cf"}, "crash_round_data": {"note": "This instruction creates a new crash game round", "possible_fields": {"timestamp_or_seed": "7049826862270272618", "game_parameters": "35ead926cf", "u32_value": **********, "u64_value": "2799526158383055917", "u8_flag": 207}, "custom_game_id": "fce5bfc6"}}]}