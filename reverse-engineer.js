import fs from 'fs';
import { Buffer } from 'buffer';
import { deserialize, serialize } from 'borsh';

/**
 * Decodes Solana transaction data for program 6YVBrao9DSLVGk7QsXxQVPbs4XyHVtHw9oSTTXw1otEW
 */

// Define possible instruction schemas based on the log messages
// From the logs we can see: CreateCrashRound, StartCrashRound, EndCrashRound

// Common instruction discriminator (first byte usually indicates instruction type)
const INSTRUCTION_TYPES = {
  0: 'CreateCrashRound',
  1: 'StartCrashRound', 
  2: 'EndCrashRound'
};

// Schema for CreateCrashRound instruction
class CreateCrashRoundInstruction {
  constructor(fields) {
    this.instruction_type = fields.instruction_type;
    this.custom_id = fields.custom_id;
    // Add other fields as needed
  }
}

// Schema for StartCrashRound instruction  
class StartCrashRoundInstruction {
  constructor(fields) {
    this.instruction_type = fields.instruction_type;
    // Add other fields as needed
  }
}

// Schema for EndCrashRound instruction
class EndCrashRoundInstruction {
  constructor(fields) {
    this.instruction_type = fields.instruction_type;
    // Add other fields as needed
  }
}

// Borsh schemas
const CREATE_CRASH_ROUND_SCHEMA = new Map([
  [CreateCrashRoundInstruction, {
    kind: 'struct',
    fields: [
      ['instruction_type', 'u8'],
      ['custom_id', [4]] // 4 bytes for custom_id based on log "fce5bfc6" (4 bytes hex)
    ]
  }]
]);

const START_CRASH_ROUND_SCHEMA = new Map([
  [StartCrashRoundInstruction, {
    kind: 'struct', 
    fields: [
      ['instruction_type', 'u8']
    ]
  }]
]);

const END_CRASH_ROUND_SCHEMA = new Map([
  [EndCrashRoundInstruction, {
    kind: 'struct',
    fields: [
      ['instruction_type', 'u8']
    ]
  }]
]);

/**
 * Decodes base64 instruction data using Borsh
 * @param {string} base64Data - Base64 encoded instruction data
 * @returns {Object} Decoded instruction data
 */
function decodeInstructionData(base64Data) {
  try {
    // Convert base64 to buffer
    const buffer = Buffer.from(base64Data, 'base64');
    console.log('Raw buffer:', buffer);
    console.log('Buffer hex:', buffer.toString('hex'));
    console.log('Buffer length:', buffer.length);
    
    // First byte is usually the instruction discriminator
    const instructionType = buffer[0];
    console.log('Instruction type byte:', instructionType);
    
    let decodedData;
    
    switch (instructionType) {
      case 0:
        // CreateCrashRound
        try {
          decodedData = deserialize(CREATE_CRASH_ROUND_SCHEMA, CreateCrashRoundInstruction, buffer);
          decodedData.instruction_name = 'CreateCrashRound';
          
          // Convert custom_id bytes to hex string
          if (decodedData.custom_id) {
            decodedData.custom_id_hex = Buffer.from(decodedData.custom_id).toString('hex');
          }
        } catch (e) {
          console.log('Failed to decode as CreateCrashRound, trying raw parsing...');
          decodedData = parseCreateCrashRoundRaw(buffer);
        }
        break;
        
      case 1:
        // StartCrashRound
        decodedData = deserialize(START_CRASH_ROUND_SCHEMA, StartCrashRoundInstruction, buffer);
        decodedData.instruction_name = 'StartCrashRound';
        break;
        
      case 2:
        // EndCrashRound
        decodedData = deserialize(END_CRASH_ROUND_SCHEMA, EndCrashRoundInstruction, buffer);
        decodedData.instruction_name = 'EndCrashRound';
        break;
        
      default:
        // Unknown instruction type, try to parse manually
        console.log('Unknown instruction type, attempting manual parsing...');
        decodedData = parseUnknownInstruction(buffer);
        break;
    }
    
    return decodedData;
    
  } catch (error) {
    console.error('Error decoding instruction data:', error);
    
    // Fallback: return raw data analysis
    const buffer = Buffer.from(base64Data, 'base64');
    return {
      error: error.message,
      raw_data: {
        base64: base64Data,
        hex: buffer.toString('hex'),
        bytes: Array.from(buffer),
        length: buffer.length,
        possible_instruction_type: buffer[0],
        instruction_name: INSTRUCTION_TYPES[buffer[0]] || 'Unknown'
      }
    };
  }
}

/**
 * Manual parsing for CreateCrashRound when Borsh fails
 */
function parseCreateCrashRoundRaw(buffer) {
  return {
    instruction_name: 'CreateCrashRound',
    instruction_type: buffer[0],
    custom_id: Array.from(buffer.slice(1, 5)), // Next 4 bytes
    custom_id_hex: buffer.slice(1, 5).toString('hex'),
    remaining_data: buffer.length > 5 ? Array.from(buffer.slice(5)) : null,
    remaining_data_hex: buffer.length > 5 ? buffer.slice(5).toString('hex') : null
  };
}

/**
 * Parse unknown instruction types
 */
function parseUnknownInstruction(buffer) {
  const result = {
    instruction_name: INSTRUCTION_TYPES[buffer[0]] || 'Unknown',
    instruction_type: buffer[0],
    raw_data: Array.from(buffer),
    hex_data: buffer.toString('hex'),
    length: buffer.length
  };

  // Try to find the custom ID "fce5bfc6" in the data
  const hexString = buffer.toString('hex');
  const customIdPattern = 'fce5bfc6';

  // Search for the custom ID in different byte orders
  if (hexString.includes(customIdPattern)) {
    result.custom_id_found = customIdPattern;
    result.custom_id_position = hexString.indexOf(customIdPattern) / 2;
  }

  // Try reverse byte order (little endian)
  const reversedCustomId = 'c6bfe5fc';
  if (hexString.includes(reversedCustomId)) {
    result.custom_id_found_reversed = reversedCustomId;
    result.custom_id_position_reversed = hexString.indexOf(reversedCustomId) / 2;
  }

  // Manual analysis based on known structure
  // From logs: "CreateCrashRound" with custom ID "fce5bfc6"
  // Let's try different interpretations of the data

  // Check if this could be a discriminator + data structure
  if (buffer.length >= 8) {
    // Try 8-byte discriminator (common in Anchor programs)
    const discriminator = buffer.slice(0, 8);
    const data = buffer.slice(8);

    result.possible_8byte_discriminator = {
      discriminator_hex: discriminator.toString('hex'),
      discriminator_bytes: Array.from(discriminator),
      remaining_data_hex: data.toString('hex'),
      remaining_data_bytes: Array.from(data)
    };

    // Check if remaining data contains our custom ID
    if (data.length >= 4) {
      for (let i = 0; i <= data.length - 4; i++) {
        const fourBytes = data.slice(i, i + 4);
        const fourBytesHex = fourBytes.toString('hex');
        const fourBytesReversed = Buffer.from(fourBytes).reverse().toString('hex');

        if (fourBytesHex === customIdPattern || fourBytesReversed === customIdPattern) {
          result.custom_id_match = {
            position_in_data: i,
            hex: fourBytesHex,
            hex_reversed: fourBytesReversed,
            matches_expected: fourBytesHex === customIdPattern ? 'direct' : 'reversed'
          };
          break;
        }
      }
    }
  }

  return result;
}

/**
 * Analyze instruction data more deeply
 */
function analyzeInstructionData(base64Data, logMessages) {
  const buffer = Buffer.from(base64Data, 'base64');
  const hexData = buffer.toString('hex');

  // Extract custom ID from log messages
  const customIdLog = logMessages.find(log => log.includes('custom ID:'));
  const customId = customIdLog ? customIdLog.split('custom ID: ')[1] : null;

  console.log('Expected custom ID from logs:', customId);

  // Try to find patterns or compute the custom ID
  const analysis = {
    raw_hex: hexData,
    raw_bytes: Array.from(buffer),
    expected_custom_id: customId,
    length: buffer.length
  };

  // Check if this is an Anchor program discriminator
  if (buffer.length >= 8) {
    const discriminator = buffer.slice(0, 8);
    const data = buffer.slice(8);

    analysis.anchor_style = {
      discriminator_hex: discriminator.toString('hex'),
      discriminator_bytes: Array.from(discriminator),
      data_hex: data.toString('hex'),
      data_bytes: Array.from(data),
      data_length: data.length
    };

    // Try to interpret the data section
    if (data.length > 0) {
      // Try different interpretations
      analysis.data_interpretations = [];

      // As raw bytes
      analysis.data_interpretations.push({
        type: 'raw_bytes',
        value: Array.from(data)
      });

      // As little-endian integers of different sizes
      if (data.length >= 4) {
        analysis.data_interpretations.push({
          type: 'u32_little_endian',
          value: data.readUInt32LE(0)
        });
      }

      if (data.length >= 8) {
        analysis.data_interpretations.push({
          type: 'u64_little_endian',
          value: data.readBigUInt64LE(0).toString()
        });
      }

      // Try to find if any 4-byte sequence could be related to the custom ID
      for (let i = 0; i <= data.length - 4; i++) {
        const fourBytes = data.slice(i, i + 4);
        const asU32LE = fourBytes.readUInt32LE(0);
        const asU32BE = fourBytes.readUInt32BE(0);

        analysis.data_interpretations.push({
          type: `4_bytes_at_offset_${i}`,
          hex: fourBytes.toString('hex'),
          u32_le: asU32LE,
          u32_be: asU32BE,
          u32_le_hex: asU32LE.toString(16).padStart(8, '0'),
          u32_be_hex: asU32BE.toString(16).padStart(8, '0')
        });
      }
    }
  }

  return analysis;
}

/**
 * Main function to process the transaction JSON and decode relevant instructions
 * @param {string} jsonFilePath - Path to the transaction JSON file
 */
function processTransactionData(jsonFilePath) {
  try {
    // Read the JSON file
    const transactionData = JSON.parse(fs.readFileSync(jsonFilePath, 'utf8'));
    
    // Find instructions with the target program ID
    const targetProgramId = '6YVBrao9DSLVGk7QsXxQVPbs4XyHVtHw9oSTTXw1otEW';
    const instructions = transactionData.transaction.message.instructions;
    
    const targetInstructions = instructions.filter(
      instruction => instruction.programId === targetProgramId
    );
    
    console.log(`Found ${targetInstructions.length} instruction(s) for program ${targetProgramId}`);
    
    const decodedInstructions = targetInstructions.map((instruction, index) => {
      console.log(`\n--- Decoding Instruction ${index + 1} ---`);
      console.log('Raw instruction data:', instruction.data);

      const decoded = decodeInstructionData(instruction.data);
      const analysis = analyzeInstructionData(instruction.data, transactionData.meta.logMessages);

      return {
        instruction_index: index,
        accounts: instruction.accounts,
        raw_data: instruction.data,
        decoded_data: decoded,
        detailed_analysis: analysis
      };
    });
    
    return {
      transaction_signature: transactionData.transaction.signatures[0],
      block_time: transactionData.blockTime,
      slot: transactionData.slot,
      program_id: targetProgramId,
      decoded_instructions: decodedInstructions,
      log_messages: transactionData.meta.logMessages
    };
    
  } catch (error) {
    console.error('Error processing transaction data:', error);
    return { error: error.message };
  }
}

/**
 * Try to compute the custom ID from the instruction data
 */
async function computeCustomId(buffer) {
  const crypto = await import('crypto');

  // Try different hashing approaches
  const results = {};

  // Hash the entire buffer
  results.full_buffer_sha256 = crypto.createHash('sha256').update(buffer).digest('hex').slice(0, 8);
  results.full_buffer_md5 = crypto.createHash('md5').update(buffer).digest('hex').slice(0, 8);

  // Hash just the data part (after 8-byte discriminator)
  if (buffer.length > 8) {
    const dataOnly = buffer.slice(8);
    results.data_only_sha256 = crypto.createHash('sha256').update(dataOnly).digest('hex').slice(0, 8);
    results.data_only_md5 = crypto.createHash('md5').update(dataOnly).digest('hex').slice(0, 8);
  }

  // Try CRC32 or simple checksums
  let crc32 = 0;
  for (let i = 0; i < buffer.length; i++) {
    crc32 = ((crc32 << 8) ^ buffer[i]) >>> 0;
  }
  results.simple_crc = crc32.toString(16).padStart(8, '0');

  // Try XOR of all bytes
  let xorResult = 0;
  for (let i = 0; i < buffer.length; i++) {
    xorResult ^= buffer[i];
  }
  results.xor_all_bytes = xorResult.toString(16).padStart(2, '0');

  return results;
}

// Main execution
if (import.meta.url === `file://${process.argv[1]}`) {
  const result = processTransactionData('./example.json');
  console.log('\n=== DECODED TRANSACTION DATA ===');
  console.log(JSON.stringify(result, null, 2));

  // Try to compute the custom ID
  if (result.decoded_instructions && result.decoded_instructions.length > 0) {
    const instruction = result.decoded_instructions[0];
    const buffer = Buffer.from(instruction.raw_data, 'base64');

    console.log('\n=== CUSTOM ID COMPUTATION ATTEMPTS ===');
    computeCustomId(buffer).then(computedIds => {
      console.log('Expected custom ID:', 'fce5bfc6');
      console.log('Computed possibilities:', computedIds);

      // Check if any computed ID matches
      const expected = 'fce5bfc6';
      for (const [method, computed] of Object.entries(computedIds)) {
        if (computed === expected) {
          console.log(`✅ MATCH FOUND: ${method} produces the expected custom ID!`);
        }
      }
    });
  }

  // Save the decoded result
  fs.writeFileSync('./decoded-transaction.json', JSON.stringify(result, null, 2));
  console.log('\nDecoded data saved to decoded-transaction.json');
}

export { decodeInstructionData, processTransactionData };
