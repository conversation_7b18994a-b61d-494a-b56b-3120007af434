# Crash Game Server Seed Analysis System

This system captures and analyzes all 3 stages of Solana crash game transactions to reverse engineer the server seed generation process.

## 🎯 **Goal**
Capture complete game data (CreateCrashRound → StartCrashRound → EndCrashRound) and correlate with API data to find how the `hashedServerSeed` is generated from transaction data.

## 📁 **Files Overview**

### Core Files
- **`app.js`** - Main WebSocket listener that captures live transactions
- **`reverse-engineer.js`** - Transaction decoder using Borsh
- **`test-game-tracking.js`** - Test script for the tracking system

### Generated Data
- **`complete-games/`** - Complete game datasets with all 3 stages
- **`logs/`** - Individual transaction files
- **`analysis/`** - Detailed analysis files

## 🔄 **Game Flow Tracking**

The system tracks each game through 3 stages:

1. **CreateCrashRound** 
   - Creates new game round
   - Generates random UUID (custom ID like `fce5bfc6`)
   - Contains 13 bytes of instruction data
   - Correlates with API `newGameTransactionHash`

2. **StartCrashRound**
   - Starts the actual game
   - Correlates with API `startGameTransactionHash`

3. **EndCrashRound** 
   - Ends the game with crash multiplier
   - Correlates with API `finishGameTransactionHash`

## 🚀 **How to Use**

### 1. Start Live Monitoring
```bash
node app.js
```
This will:
- Connect to Solana WebSocket
- Monitor program `6YVBrao9DSLVGk7QsXxQVPbs4XyHVtHw9oSTTXw1otEW`
- Capture all 3 stages of each game
- Correlate with API data from `https://backend.solpump.com/api/v1/crash/game/current`
- Save complete game datasets

### 2. Test the System
```bash
node test-game-tracking.js
```
This creates a mock complete game for testing.

### 3. Analyze Existing Data
```bash
node reverse-engineer.js
```
This analyzes the example transaction data.

## 📊 **Data Structure**

Each complete game file contains:

```json
{
  "game_info": {
    "custom_id": "fce5bfc6",           // UUID prefix from logs
    "api_game_id": "085ad62a-...",     // Full UUID from API
    "start_time": "2025-06-24T...",
    "is_complete": true
  },
  "api_data": {
    "hashedServerSeed": "iF+JU5v9a9DEDH/bodn0/XbQG9V9wiOgprLn/ppeMNM=",
    "clientSeed": "nevNXNI6kKH8s8fbsFdMVDE9M0x3G1TTqbUuuGnyv8g=",
    "crashMultiplier": 2.45,
    // ... other API fields
  },
  "stages": {
    "CreateCrashRound": {
      "decoded_instructions": [{
        "decoded_data": {
          "instruction_name": "CreateCrashRound",
          "discriminator_hex": "d80d78f2448503ab",
          "data_hex": "6a54a0fd2d04d66135ead926cf",
          "data_length": 13
        }
      }]
    },
    "StartCrashRound": { /* ... */ },
    "EndCrashRound": { /* ... */ }
  }
}
```

## 🔍 **Server Seed Analysis**

The system attempts to find correlations between:

1. **Individual Stage Data**
   - Hash of CreateCrashRound data
   - Hash of StartCrashRound data  
   - Hash of EndCrashRound data

2. **Combined Stage Data**
   - Hash of all stages concatenated
   - Hash of different stage combinations

3. **Additional Entropy**
   - Transaction signatures
   - Block timestamps
   - Game IDs
   - Custom IDs

## 🎲 **Current Findings**

- **Custom ID**: `fce5bfc6` is first part of random UUID, not derived from transaction data
- **Discriminator**: `d80d78f2448503ab` identifies CreateCrashRound instruction
- **Data Structure**: 13 bytes of instruction parameters in each stage
- **Server Seed**: Likely generated server-side with additional entropy not visible on-chain

## 📈 **Next Steps**

1. **Collect Live Data**: Run `app.js` to capture complete games
2. **Pattern Analysis**: Look for patterns across multiple complete games
3. **Timing Analysis**: Check if server seed correlates with precise timestamps
4. **Multi-Game Analysis**: Compare server seeds across different games

## 🔧 **Technical Details**

- **Program ID**: `6YVBrao9DSLVGk7QsXxQVPbs4XyHVtHw9oSTTXw1otEW`
- **Anchor Discriminators**: 8-byte instruction identifiers
- **Data Encoding**: Base64 → Hex → Borsh decoding
- **API Correlation**: Real-time matching with backend API

## 🎯 **Success Criteria**

The system will be successful when we can:
1. ✅ Capture complete 3-stage game data
2. ✅ Decode all instruction data
3. ✅ Correlate with API data
4. ⏳ Find server seed generation pattern
5. ⏳ Predict server seed from transaction data

---

**Status**: System is ready for live data collection. Run `node app.js` and wait for complete games to be captured!
