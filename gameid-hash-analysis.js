import fs from 'fs';
import crypto from 'crypto';

/**
 * Comprehensive analysis of game ID UUID hashing to find server seed
 */

// Load all complete game files
const gameFiles = [
  'complete-game-93be55a8.json',
  'complete-game-acfd826c.json', 
  'complete-game-ad84e15d.json'
];

const games = gameFiles.map(file => {
  try {
    return JSON.parse(fs.readFileSync(`./complete-games/${file}`, 'utf8'));
  } catch (e) {
    console.log(`Could not load ${file}`);
    return null;
  }
}).filter(Boolean);

console.log('🎯 GAME ID UUID HASHING ANALYSIS');
console.log('================================');
console.log(`Analyzing ${games.length} complete games`);

// Extract game data
const gameData = games.map(game => ({
  customId: game.game_info.custom_id,
  fullGameId: game.game_info.api_game_id,
  serverSeed: game.api_snapshots.EndCrashRound?.serverSeed,
  clientSeed: game.api_snapshots.EndCrashRound?.clientSeed,
  crashMultiplier: game.api_snapshots.EndCrashRound?.crashMultiplier
}));

function tryGameIdHashing(game) {
  console.log(`\n🎯 GAME ${game.customId}:`);
  console.log(`Full Game ID: ${game.fullGameId}`);
  console.log(`Target Server Seed: ${game.serverSeed}`);
  console.log('='.repeat(60));
  
  const matches = [];
  
  // 1. Direct UUID hashing
  console.log('\n1️⃣ DIRECT UUID HASHING:');
  
  const directAttempts = [
    { name: 'SHA256(fullGameId)', value: game.fullGameId },
    { name: 'SHA256(customId)', value: game.customId },
    { name: 'SHA256(fullGameId without dashes)', value: game.fullGameId.replace(/-/g, '') },
    { name: 'SHA256(fullGameId uppercase)', value: game.fullGameId.toUpperCase() },
    { name: 'SHA256(fullGameId lowercase)', value: game.fullGameId.toLowerCase() },
    { name: 'SHA256(customId uppercase)', value: game.customId.toUpperCase() },
    { name: 'SHA256(customId lowercase)', value: game.customId.toLowerCase() }
  ];
  
  directAttempts.forEach(attempt => {
    const hash = crypto.createHash('sha256').update(attempt.value, 'utf8').digest('hex');
    console.log(`${attempt.name}: ${hash.substring(0, 32)}...`);
    
    if (hash === game.serverSeed) {
      console.log(`🎉 BREAKTHROUGH! ${attempt.name} = server seed!`);
      matches.push(attempt);
    }
  });
  
  // 2. UUID transformations
  console.log('\n2️⃣ UUID TRANSFORMATIONS:');
  
  const uuid = game.fullGameId;
  const uuidNoDashes = uuid.replace(/-/g, '');
  
  // Split UUID into parts
  const parts = uuid.split('-');
  const [part1, part2, part3, part4, part5] = parts;
  
  console.log(`UUID parts: ${part1} | ${part2} | ${part3} | ${part4} | ${part5}`);
  
  const transformations = [
    { name: 'SHA256(reversed UUID)', value: uuid.split('').reverse().join('') },
    { name: 'SHA256(reversed UUID no dashes)', value: uuidNoDashes.split('').reverse().join('') },
    { name: 'SHA256(part1 + part5)', value: part1 + part5 },
    { name: 'SHA256(part2 + part4)', value: part2 + part4 },
    { name: 'SHA256(part3 only)', value: part3 },
    { name: 'SHA256(parts reversed order)', value: part5 + part4 + part3 + part2 + part1 },
    { name: 'SHA256(parts without part1)', value: part2 + part3 + part4 + part5 },
    { name: 'SHA256(first 16 chars)', value: uuidNoDashes.substring(0, 16) },
    { name: 'SHA256(last 16 chars)', value: uuidNoDashes.substring(16) },
    { name: 'SHA256(middle 16 chars)', value: uuidNoDashes.substring(8, 24) }
  ];
  
  transformations.forEach(attempt => {
    const hash = crypto.createHash('sha256').update(attempt.value, 'utf8').digest('hex');
    console.log(`${attempt.name}: ${hash.substring(0, 32)}...`);
    
    if (hash === game.serverSeed) {
      console.log(`🎉 BREAKTHROUGH! ${attempt.name} = server seed!`);
      matches.push(attempt);
    }
  });
  
  // 3. UUID as binary data
  console.log('\n3️⃣ UUID AS BINARY DATA:');
  
  const binaryAttempts = [
    { name: 'SHA256(UUID as hex buffer)', data: Buffer.from(uuidNoDashes, 'hex') },
    { name: 'SHA256(UUID as UTF8 buffer)', data: Buffer.from(uuid, 'utf8') },
    { name: 'SHA256(customId as hex buffer)', data: Buffer.from(game.customId, 'hex') }
  ];
  
  binaryAttempts.forEach(attempt => {
    const hash = crypto.createHash('sha256').update(attempt.data).digest('hex');
    console.log(`${attempt.name}: ${hash.substring(0, 32)}...`);
    
    if (hash === game.serverSeed) {
      console.log(`🎉 BREAKTHROUGH! ${attempt.name} = server seed!`);
      matches.push({ name: attempt.name, value: attempt.data.toString('hex') });
    }
  });
  
  // 4. UUID with prefixes/suffixes
  console.log('\n4️⃣ UUID WITH PREFIXES/SUFFIXES:');
  
  const prefixSuffixAttempts = [
    { name: 'SHA256("game:" + UUID)', value: 'game:' + uuid },
    { name: 'SHA256("crash:" + UUID)', value: 'crash:' + uuid },
    { name: 'SHA256("seed:" + UUID)', value: 'seed:' + uuid },
    { name: 'SHA256("solpump:" + UUID)', value: 'solpump:' + uuid },
    { name: 'SHA256(UUID + ":game")', value: uuid + ':game' },
    { name: 'SHA256(UUID + ":seed")', value: uuid + ':seed' },
    { name: 'SHA256(UUID + customId)', value: uuid + game.customId },
    { name: 'SHA256(customId + UUID)', value: game.customId + uuid }
  ];
  
  prefixSuffixAttempts.forEach(attempt => {
    const hash = crypto.createHash('sha256').update(attempt.value, 'utf8').digest('hex');
    console.log(`${attempt.name}: ${hash.substring(0, 32)}...`);
    
    if (hash === game.serverSeed) {
      console.log(`🎉 BREAKTHROUGH! ${attempt.name} = server seed!`);
      matches.push(attempt);
    }
  });
  
  // 5. UUID rotations and shifts
  console.log('\n5️⃣ UUID ROTATIONS AND SHIFTS:');
  
  const rotationAttempts = [];
  
  // Try rotating the UUID string by different positions
  for (let i = 1; i < uuid.length; i += 4) {
    const rotated = uuid.substring(i) + uuid.substring(0, i);
    rotationAttempts.push({ name: `SHA256(UUID rotated ${i})`, value: rotated });
  }
  
  // Try rotating the UUID without dashes
  for (let i = 1; i < uuidNoDashes.length; i += 4) {
    const rotated = uuidNoDashes.substring(i) + uuidNoDashes.substring(0, i);
    rotationAttempts.push({ name: `SHA256(UUID no-dash rotated ${i})`, value: rotated });
  }
  
  rotationAttempts.forEach(attempt => {
    const hash = crypto.createHash('sha256').update(attempt.value, 'utf8').digest('hex');
    
    if (hash === game.serverSeed) {
      console.log(`🎉 BREAKTHROUGH! ${attempt.name} = server seed!`);
      console.log(`   Input: ${attempt.value}`);
      matches.push(attempt);
    }
  });
  
  if (rotationAttempts.length > 0) {
    console.log(`Tested ${rotationAttempts.length} rotation variations...`);
  }
  
  // 6. UUID XOR operations
  console.log('\n6️⃣ UUID XOR OPERATIONS:');
  
  const uuidBuffer = Buffer.from(uuidNoDashes, 'hex');
  
  // XOR with different patterns
  const xorPatterns = [
    Buffer.alloc(16, 0xFF), // All 1s
    Buffer.alloc(16, 0xAA), // Alternating pattern
    Buffer.alloc(16, 0x55), // Alternating pattern
    Buffer.from(game.customId.padEnd(32, '0'), 'hex') // Custom ID as pattern
  ];
  
  xorPatterns.forEach((pattern, idx) => {
    const xorResult = Buffer.alloc(16);
    for (let i = 0; i < 16; i++) {
      xorResult[i] = uuidBuffer[i] ^ pattern[i % pattern.length];
    }
    
    const hash = crypto.createHash('sha256').update(xorResult).digest('hex');
    console.log(`SHA256(UUID XOR pattern${idx + 1}): ${hash.substring(0, 32)}...`);
    
    if (hash === game.serverSeed) {
      console.log(`🎉 BREAKTHROUGH! UUID XOR pattern${idx + 1} = server seed!`);
      matches.push({ name: `UUID XOR pattern${idx + 1}`, value: xorResult.toString('hex') });
    }
  });
  
  // 7. Check for partial matches
  console.log('\n7️⃣ CHECKING FOR PARTIAL MATCHES:');
  
  // Re-check all attempts for partial matches (first 16 characters)
  const allAttempts = [...directAttempts, ...transformations, ...prefixSuffixAttempts];
  
  const partialMatches = [];
  allAttempts.forEach(attempt => {
    const hash = crypto.createHash('sha256').update(attempt.value, 'utf8').digest('hex');
    if (hash.substring(0, 16) === game.serverSeed.substring(0, 16)) {
      partialMatches.push({ ...attempt, hash });
    }
  });
  
  if (partialMatches.length > 0) {
    console.log('✅ Found partial matches (first 16 chars):');
    partialMatches.forEach(match => {
      console.log(`   ${match.name}: ${match.hash.substring(0, 16)}...`);
    });
  } else {
    console.log('❌ No partial matches found');
  }
  
  return matches;
}

// Analyze each game
const allMatches = [];
gameData.forEach(game => {
  const matches = tryGameIdHashing(game);
  if (matches.length > 0) {
    allMatches.push({ gameId: game.customId, matches });
  }
});

console.log('\n📊 FINAL RESULTS:');
console.log('=================');

if (allMatches.length > 0) {
  console.log(`🎉 FOUND ${allMatches.length} GAME(S) WITH MATCHES!`);
  allMatches.forEach(result => {
    console.log(`\nGame ${result.gameId}:`);
    result.matches.forEach(match => {
      console.log(`  ✅ ${match.name}`);
      console.log(`     Input: ${match.value}`);
    });
  });
} else {
  console.log('❌ No exact matches found across all games');
  console.log('\nThis suggests:');
  console.log('1. Server seed is not directly derived from game ID');
  console.log('2. More complex transformation might be involved');
  console.log('3. Additional entropy is mixed with game ID');
  console.log('4. Server seed generation uses external randomness');
}

console.log('\n📋 ANALYSIS SUMMARY:');
console.log('===================');
console.log('✅ Tested direct UUID hashing');
console.log('✅ Tried UUID transformations (reverse, parts, etc.)');
console.log('✅ Analyzed UUID as binary data');
console.log('✅ Tested prefixes and suffixes');
console.log('✅ Tried rotations and shifts');
console.log('✅ Tested XOR operations');
console.log('✅ Checked for partial matches');

// Save results
const analysisResults = {
  summary: {
    total_games: gameData.length,
    games_with_matches: allMatches.length,
    analysis_timestamp: new Date().toISOString()
  },
  games: gameData,
  matches: allMatches
};

fs.writeFileSync('./gameid-hash-analysis.json', JSON.stringify(analysisResults, null, 2));
console.log('\n💾 Analysis saved to gameid-hash-analysis.json');
