import fs from 'fs';
import { Buffer } from 'buffer';

/**
 * Comprehensive analysis of CreateCrashRound instruction data
 * Extract all possible interpretations of the 13 bytes
 */

// Load the complete game data
const gameData = JSON.parse(fs.readFileSync('./complete-games/complete-game-93be55a8.json', 'utf8'));

// Extract the CreateCrashRound data
const createStage = gameData.stages.CreateCrashRound;
const instruction = createStage.transaction.transaction.message.instructions.find(
  inst => inst.programId === '6YVBrao9DSLVGk7QsXxQVPbs4XyHVtHw9oSTTXw1otEW'
);

const base64Data = instruction.data;
const buffer = Buffer.from(base64Data, 'base64');
const discriminator = buffer.slice(0, 8);
const data = buffer.slice(8); // The 13 bytes we want to analyze

console.log('🔍 COMPREHENSIVE CreateCrashRound DATA ANALYSIS');
console.log('===============================================');
console.log('Game ID:', gameData.game_info.custom_id);
console.log('Full Game ID:', gameData.game_info.api_game_id);
console.log('Server Seed:', gameData.api_snapshots.EndCrashRound.serverSeed);
console.log('Crash Multiplier:', gameData.api_snapshots.EndCrashRound.crashMultiplier);
console.log('Block Time:', createStage.transaction.blockTime);
console.log('Slot:', createStage.transaction.slot);
console.log('');
console.log('Raw Data (13 bytes):', data.toString('hex'));
console.log('Raw Bytes:', Array.from(data));

const interpretations = [];

// 1. BASIC DATA TYPE INTERPRETATIONS
console.log('\n📊 BASIC DATA TYPE INTERPRETATIONS');
console.log('==================================');

// Single bytes
for (let i = 0; i < data.length; i++) {
  const byte = data[i];
  interpretations.push({
    type: `byte_${i}`,
    description: `Byte ${i}`,
    value: byte,
    hex: byte.toString(16).padStart(2, '0'),
    binary: byte.toString(2).padStart(8, '0'),
    ascii: byte >= 32 && byte <= 126 ? String.fromCharCode(byte) : 'non-printable'
  });
}

// 16-bit integers (2 bytes each)
for (let i = 0; i <= data.length - 2; i++) {
  const u16_le = data.readUInt16LE(i);
  const u16_be = data.readUInt16BE(i);
  interpretations.push({
    type: `u16_${i}`,
    description: `16-bit uint at byte ${i}`,
    value_le: u16_le,
    value_be: u16_be,
    hex_le: u16_le.toString(16),
    hex_be: u16_be.toString(16)
  });
}

// 32-bit integers (4 bytes each)
for (let i = 0; i <= data.length - 4; i++) {
  const u32_le = data.readUInt32LE(i);
  const u32_be = data.readUInt32BE(i);
  const i32_le = data.readInt32LE(i);
  const i32_be = data.readInt32BE(i);
  
  interpretations.push({
    type: `u32_${i}`,
    description: `32-bit uint at byte ${i}`,
    value_le: u32_le,
    value_be: u32_be,
    signed_le: i32_le,
    signed_be: i32_be,
    hex_le: u32_le.toString(16),
    hex_be: u32_be.toString(16),
    timestamp_check: Math.abs(u32_le - createStage.transaction.blockTime) < 3600 ? 'Could be timestamp!' : 'Not timestamp'
  });
}

// 64-bit integers (8 bytes each)
for (let i = 0; i <= data.length - 8; i++) {
  const u64_le = data.readBigUInt64LE(i);
  const u64_be = data.readBigUInt64BE(i);
  const i64_le = data.readBigInt64LE(i);
  const i64_be = data.readBigInt64BE(i);
  
  interpretations.push({
    type: `u64_${i}`,
    description: `64-bit uint at byte ${i}`,
    value_le: u64_le.toString(),
    value_be: u64_be.toString(),
    signed_le: i64_le.toString(),
    signed_be: i64_be.toString(),
    hex_le: u64_le.toString(16),
    hex_be: u64_be.toString(16),
    timestamp_ms_check: Math.abs(Number(u64_le) - createStage.transaction.blockTime * 1000) < 3600000 ? 'Could be timestamp in ms!' : 'Not timestamp',
    slot_check: Number(u64_le) === createStage.transaction.slot ? 'Matches slot!' : 'Not slot'
  });
}

// 2. FLOATING POINT INTERPRETATIONS
console.log('\n🔢 FLOATING POINT INTERPRETATIONS');
console.log('=================================');

for (let i = 0; i <= data.length - 4; i++) {
  const float_le = data.readFloatLE(i);
  const float_be = data.readFloatBE(i);
  interpretations.push({
    type: `float_${i}`,
    description: `32-bit float at byte ${i}`,
    value_le: float_le,
    value_be: float_be,
    multiplier_check: Math.abs(float_le - gameData.api_snapshots.EndCrashRound.crashMultiplier) < 0.01 ? 'Could be crash multiplier!' : 'Not multiplier'
  });
}

for (let i = 0; i <= data.length - 8; i++) {
  const double_le = data.readDoubleLE(i);
  const double_be = data.readDoubleBE(i);
  interpretations.push({
    type: `double_${i}`,
    description: `64-bit double at byte ${i}`,
    value_le: double_le,
    value_be: double_be,
    multiplier_check: Math.abs(double_le - gameData.api_snapshots.EndCrashRound.crashMultiplier) < 0.01 ? 'Could be crash multiplier!' : 'Not multiplier'
  });
}

// 3. GAME-SPECIFIC INTERPRETATIONS
console.log('\n🎮 GAME-SPECIFIC INTERPRETATIONS');
console.log('================================');

const gameId = gameData.game_info.custom_id;
const serverSeed = gameData.api_snapshots.EndCrashRound.serverSeed;
const crashMultiplier = gameData.api_snapshots.EndCrashRound.crashMultiplier;

// Check if any 4-byte sequence matches parts of game ID
for (let i = 0; i <= data.length - 4; i++) {
  const fourBytes = data.slice(i, i + 4).toString('hex');
  interpretations.push({
    type: `game_id_check_${i}`,
    description: `4 bytes at ${i} vs game ID`,
    value: fourBytes,
    matches_game_id: gameId.includes(fourBytes),
    reversed_matches: gameId.includes(Buffer.from(fourBytes, 'hex').reverse().toString('hex'))
  });
}

// Check for seed material
for (let i = 0; i <= data.length - 8; i++) {
  const eightBytes = data.slice(i, i + 8).toString('hex');
  interpretations.push({
    type: `seed_check_${i}`,
    description: `8 bytes at ${i} vs server seed`,
    value: eightBytes,
    in_server_seed: serverSeed.includes(eightBytes),
    reversed_in_seed: serverSeed.includes(Buffer.from(eightBytes, 'hex').reverse().toString('hex'))
  });
}

// 4. STRUCTURED DATA INTERPRETATIONS
console.log('\n🏗️ STRUCTURED DATA INTERPRETATIONS');
console.log('===================================');

// Common game parameter structures
const structures = [
  {
    name: 'timestamp_u32 + amount_u64 + flag_u8',
    fields: [
      { name: 'timestamp', type: 'u32', offset: 0 },
      { name: 'amount', type: 'u64', offset: 4 },
      { name: 'flag', type: 'u8', offset: 12 }
    ]
  },
  {
    name: 'seed_u64 + multiplier_f32 + flag_u8',
    fields: [
      { name: 'seed', type: 'u64', offset: 0 },
      { name: 'multiplier', type: 'f32', offset: 8 },
      { name: 'flag', type: 'u8', offset: 12 }
    ]
  },
  {
    name: 'game_id_u32 + timestamp_u64 + flag_u8',
    fields: [
      { name: 'game_id', type: 'u32', offset: 0 },
      { name: 'timestamp', type: 'u64', offset: 4 },
      { name: 'flag', type: 'u8', offset: 12 }
    ]
  },
  {
    name: 'three_u32_values + one_u8',
    fields: [
      { name: 'value1', type: 'u32', offset: 0 },
      { name: 'value2', type: 'u32', offset: 4 },
      { name: 'value3', type: 'u32', offset: 8 },
      { name: 'flag', type: 'u8', offset: 12 }
    ]
  }
];

structures.forEach(structure => {
  const parsed = {};
  let valid = true;
  
  structure.fields.forEach(field => {
    if (field.offset + getTypeSize(field.type) <= data.length) {
      parsed[field.name] = readValue(data, field.offset, field.type);
    } else {
      valid = false;
    }
  });
  
  if (valid) {
    interpretations.push({
      type: 'structure',
      description: structure.name,
      value: parsed,
      analysis: analyzeStructure(parsed, gameData)
    });
  }
});

// 5. CRYPTOGRAPHIC INTERPRETATIONS
console.log('\n🔐 CRYPTOGRAPHIC INTERPRETATIONS');
console.log('================================');

// Check if data could be hash input/output
const crypto = await import('crypto');

// Try interpreting as hash of known values
const knownValues = [
  gameId,
  gameData.game_info.api_game_id,
  createStage.transaction.blockTime.toString(),
  createStage.transaction.slot.toString(),
  createStage.signature
];

knownValues.forEach(value => {
  const hash = crypto.createHash('sha256').update(value).digest();
  const hashHex = hash.toString('hex');
  
  // Check if our data appears in this hash
  const dataHex = data.toString('hex');
  if (hashHex.includes(dataHex)) {
    interpretations.push({
      type: 'crypto_match',
      description: `Data found in SHA256(${value})`,
      value: value,
      hash: hashHex,
      position: hashHex.indexOf(dataHex)
    });
  }
});

// Print all interpretations
console.log('\n📋 ALL POSSIBLE INTERPRETATIONS');
console.log('===============================');

interpretations.forEach((interp, i) => {
  console.log(`${i + 1}. ${interp.description || interp.type}`);
  console.log(`   Type: ${interp.type}`);
  
  if (interp.value !== undefined) {
    if (typeof interp.value === 'object') {
      console.log(`   Value: ${JSON.stringify(interp.value)}`);
    } else {
      console.log(`   Value: ${interp.value}`);
    }
  }
  
  // Highlight interesting findings
  const interesting = [];
  Object.keys(interp).forEach(key => {
    if (key.includes('check') || key.includes('matches') || key.includes('analysis')) {
      const value = interp[key];
      if (value && value !== 'Not timestamp' && value !== 'Not multiplier' && value !== 'Not slot' && value !== false) {
        interesting.push(`${key}: ${value}`);
      }
    }
  });
  
  if (interesting.length > 0) {
    console.log(`   🎯 INTERESTING: ${interesting.join(', ')}`);
  }
  
  console.log('');
});

// Helper functions
function getTypeSize(type) {
  const sizes = { u8: 1, u16: 2, u32: 4, u64: 8, f32: 4, f64: 8 };
  return sizes[type] || 1;
}

function readValue(buffer, offset, type) {
  switch (type) {
    case 'u8': return buffer[offset];
    case 'u16': return buffer.readUInt16LE(offset);
    case 'u32': return buffer.readUInt32LE(offset);
    case 'u64': return buffer.readBigUInt64LE(offset).toString();
    case 'f32': return buffer.readFloatLE(offset);
    case 'f64': return buffer.readDoubleLE(offset);
    default: return buffer[offset];
  }
}

function analyzeStructure(parsed, gameData) {
  const analysis = [];
  
  Object.entries(parsed).forEach(([key, value]) => {
    if (key.includes('timestamp') && typeof value === 'number') {
      const blockTime = gameData.stages.CreateCrashRound.transaction.blockTime;
      if (Math.abs(value - blockTime) < 3600) {
        analysis.push(`${key} matches block time!`);
      }
    }
    
    if (key.includes('multiplier') && typeof value === 'number') {
      const crashMult = gameData.api_snapshots.EndCrashRound.crashMultiplier;
      if (Math.abs(value - crashMult) < 0.01) {
        analysis.push(`${key} matches crash multiplier!`);
      }
    }
    
    if (key.includes('game_id') || key.includes('seed')) {
      const gameId = gameData.game_info.custom_id;
      const serverSeed = gameData.api_snapshots.EndCrashRound.serverSeed;
      
      if (typeof value === 'string' && (gameId.includes(value) || serverSeed.includes(value))) {
        analysis.push(`${key} found in game data!`);
      }
    }
  });
  
  return analysis;
}

console.log(`\n📊 SUMMARY: Found ${interpretations.length} possible interpretations`);
console.log('Look for entries marked with 🎯 INTERESTING for the most promising leads!');
