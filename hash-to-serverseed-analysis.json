{"summary": {"total_games": 3, "total_methods_tested": 50, "analysis_timestamp": "2025-06-24T07:22:03.764Z"}, "games": [{"gameId": "93be55a8", "attempts": [{"method": "SHA256(constant)", "input": "4255143018", "output": "e7500adb39904571391df2c1c3395af421cb81c530ac7f0c152d8357b326e860"}, {"method": "SHA256(variable1)", "input": "2428267308", "output": "b43f283325f277eb5ffd77bbc37c47cbaf61ef020380b94c52ea3cbbcab1d3f5"}, {"method": "SHA256(variable2)", "input": "2639089104", "output": "1639b13e3fc71777a7aba162a4425da8ce047ea9a9f75552db595a141f3b0566"}, {"method": "SHA256(lastByte)", "input": "151", "output": "8e612bd1f5d132a339575b8dafb7842c64614e56bcf3d5ab65a0bc4b34329407"}, {"method": "SHA256(constantHex)", "input": "6a54a0fd", "output": "8a5c729109e23d38499464e09905af0537167af6831fc2f6f79c145aadd12b0f"}, {"method": "SHA256(variable1Hex)", "input": "2c6bbc90", "output": "3058501369e38598a53328511c4bb8497f209897071b55248170bcac3d08031b"}, {"method": "SHA256(variable2Hex)", "input": "d04d4d9d", "output": "c5bea19b8477b39fc5bad6a7f139e2c12409b1f9744c1933270fa86eb4a57454"}, {"method": "SHA256(lastByteHex)", "input": "97", "output": "d6d824abba4afde81129c71dea75b8100e96338da5f416d2f69088f1960cb091"}, {"method": "SHA256(instructionData)", "input": "6a54a0fd2c6bbc90d04d4d9d97", "output": "776cb0347bb8646db8ecedc9de796e8a88ab79cc8998fc5fd425963640a44187"}, {"method": "SHA256(variablePartHex)", "input": "2c6bbc90d04d4d9d97", "output": "ea8e8d4544af0fad4b114d0cfe2920c25caaa189ac30bea66c58398b3e273579"}, {"method": "SHA256(fullData)", "input": "d80d78f2448503ab6a54a0fd2c6bbc90d04d4d9d97", "output": "e52dbe4104a07de55f698586077f268005a083226a70240e7e4d570e8dbd5a90"}, {"method": "SHA256(discriminator)", "input": "d80d78f2448503ab", "output": "4e86eecf63f21a005b6205748b906996a1bbe30e0d177391f192969b0ca7367a"}, {"method": "SHA256(constant + variable1)", "input": "42551430182428267308", "output": "a4f38057b6ad4694fbe2a3570a41fa60accd77c40147c27520678b070fa80ca2"}, {"method": "SHA256(constant + variable2)", "input": "42551430182639089104", "output": "bd2ef2e11a871509821b164968b89448a2061ec2c61b0dc70009e2b5181afce2"}, {"method": "SHA256(variable1 + variable2)", "input": "24282673082639089104", "output": "a3a5b9ae63848ca4c9d5f8cd4935e8ba21a141c4bb5ffede39fab8339d4eb869"}, {"method": "SHA256(variable1 + variable2 + lastByte)", "input": "24282673082639089104151", "output": "27f2f93d7820ab4e6b0aae17fde2a939d34bfee4e513628bcce0e94723cd2674"}, {"method": "SHA256(constantHex + variable1Hex)", "input": "6a54a0fd2c6bbc90", "output": "4405dec07fb1545365c60f52f13775793cdec9ea08ead17020dfc118dd058a79"}, {"method": "SHA256(constantHex + variable2Hex)", "input": "6a54a0fdd04d4d9d", "output": "a164c9f9784ebdd124842b41c12ec38598e22dcd121a3e7f8c27c1bac5aedf92"}, {"method": "SHA256(variable1Hex + variable2Hex)", "input": "2c6bbc90d04d4d9d", "output": "c89850df7dd3b59de68b06172e4580fe339bbcb41d854de8278ef0cf3df339f3"}, {"method": "SHA256(variable1Hex + variable2Hex + lastByteHex)", "input": "2c6bbc90d04d4d9d97", "output": "ea8e8d4544af0fad4b114d0cfe2920c25caaa189ac30bea66c58398b3e273579"}, {"method": "SHA256(all variables concatenated)", "input": "24282673082639089104151", "output": "27f2f93d7820ab4e6b0aae17fde2a939d34bfee4e513628bcce0e94723cd2674"}, {"method": "SHA256(all hex concatenated)", "input": "2c6bbc90d04d4d9d97", "output": "ea8e8d4544af0fad4b114d0cfe2920c25caaa189ac30bea66c58398b3e273579"}, {"method": "SHA256(instructionData + gameId)", "input": "6a54a0fd2c6bbc90d04d4d9d9793be55a8...", "output": "a3ff9aa18af060acaf8ae248fbd8209a5e5ce6ffa5afe1a111c0cd51588e971f"}, {"method": "SHA256(instructionData + fullGameId)", "input": "6a54a0fd2c6bbc90d04d4d9d9793be55a8-b790-4fad-87bb-2900eb874415...", "output": "561e8fc8bf1cf7d729cbd48ed2c343d77aab20d7daed462680a6acf94f25b3ca"}, {"method": "SHA256(variable2Hex + gameId)", "input": "d04d4d9d93be55a8...", "output": "c21c6b3d867a9cf7f0580bdb84ca399ef723158f4079bf53c4a31188f63b988a"}, {"method": "SHA256(variable2Hex + fullGameId)", "input": "d04d4d9d93be55a8-b790-4fad-87bb-2900eb874415...", "output": "e8fd03efdcc2d1a897a7cd3c14f533bef4e4a76a4565e87710fe3d0ae91d3a6a"}, {"method": "SHA256(variablePartHex + gameId)", "input": "2c6bbc90d04d4d9d9793be55a8...", "output": "0e4e1e15a448818a801312b8afd4b40aaead2b25178cd09c6aebc6c1527ae11e"}, {"method": "SHA256(variablePartHex + fullGameId)", "input": "2c6bbc90d04d4d9d9793be55a8-b790-4fad-87bb-2900eb874415...", "output": "7e88678e4131895370eaa33888cdff3d398ee44765e61363354b8bf15dcfe694"}, {"method": "SHA256(instructionData + blockTime)", "input": "6a54a0fd2c6bbc90d04d4d9d971750747687...", "output": "2eea06a02c418c3af347a75bb319b999ba458c15c6023cf4f6f64d0b48829f6b"}, {"method": "SHA256(instructionData + slot)", "input": "6a54a0fd2c6bbc90d04d4d9d97348840187...", "output": "2887aadf3dff38064c94f079dd17141393d563ac9d471d706cd1ff831227b477"}, {"method": "SHA256(instructionData + signature)", "input": "6a54a0fd2c6bbc90d04d4d9d972emHHBvZtnKL4buc4h3a4fpmuqUJBvwQEUp7uriT7muj8vraQM2e9TW83WPeeau3KkkLts2M9a...", "output": "4d7db6c9bc5f6e0ab812f90589533cb1c8d3002ca7ddac7cb5346d4e4010fc62"}, {"method": "SHA256(variable2 + blockTime)", "input": "26390891041750747687...", "output": "79a2861110bb49341c7893d81f74486877f7560ef36f56b181b8f8581c64d035"}, {"method": "SHA256(variable2 + slot)", "input": "2639089104348840187...", "output": "718ad26f9392d01db85503109e4b5cee666d44156fa09cc083ace1b084d2b1b4"}, {"method": "MD5(instructionData)", "input": "6a54a0fd2c6bbc90d04d4d9d97", "output": "a46e37906e812e4c508aef7f94e7d424"}, {"method": "SHA1(instructionData)", "input": "6a54a0fd2c6bbc90d04d4d9d97", "output": "917aa95f26f5b605687227c2583d234ed8364dbf"}, {"method": "SHA256(instructionData)", "input": "6a54a0fd2c6bbc90d04d4d9d97", "output": "bd7ca163811f733c4fd4ad4a83d031faa3ffb6aba2fdd7af4f0825aedb67ac5d"}, {"method": "SHA512(instructionData)", "input": "6a54a0fd2c6bbc90d04d4d9d97", "output": "b450b7854a30e12cd077a05b7de0978b01333fe92da2d8e37283f13649c88596cabc764c3059bdc737dd950ffeb182e060a1c3f99713c45a9233d766fe6f527f"}, {"method": "HMAC-SHA256(instructionData, \"93be55a8\")", "input": "6a54a0fd2c6bbc90d04d4d9d97", "output": "32f4f32c29a8ef363499b1e486a58090d4239e8734435127d7a12cd18829c630"}, {"method": "HMAC-SHA256(instructionData, \"93be55a8-b790-4fad-87bb-2900eb874415\")", "input": "6a54a0fd2c6bbc90d04d4d9d97", "output": "41a18c97043c554001bf0e68bb51cb55026393f6c751cad218760fde23ab7b6f"}, {"method": "HMAC-SHA256(instructionData, \"6a54a0fd\")", "input": "6a54a0fd2c6bbc90d04d4d9d97", "output": "96af3a84616f27258cdcc8820abb9a8b0a2aac4c435e3f744f6264f5f2853c7a"}, {"method": "HMAC-SHA256(instructionData, \"d80d78f2448503ab\")", "input": "6a54a0fd2c6bbc90d04d4d9d97", "output": "edb6bd21c137880242d1f426df278e5d3b21b40040c1a208803925a0b28d8d4e"}, {"method": "HMAC-SHA256(instructionData, \"solpump\")", "input": "6a54a0fd2c6bbc90d04d4d9d97", "output": "d801bf87b3be1899996674fc9c11e46eb2863a30f3c1d09ce05dd5099fb3f0e0"}, {"method": "HMAC-SHA256(instructionData, \"crash\")", "input": "6a54a0fd2c6bbc90d04d4d9d97", "output": "afcbdd6b44c099538c93bf9d015c03bc60f1ca1486582741c7e87fe0ca2bcd34"}, {"method": "HMAC-SHA256(instructionData, \"game\")", "input": "6a54a0fd2c6bbc90d04d4d9d97", "output": "c537e6fba2fba52c6b0de4adba639ea50f44b1c368f2af8a03f3a6c7aef699d7"}, {"method": "SHA256(instructionData as binary)", "input": "6a54a0fd2c6bbc90d04d4d9d97", "output": "bd7ca163811f733c4fd4ad4a83d031faa3ffb6aba2fdd7af4f0825aedb67ac5d"}, {"method": "SHA256(variablePartHex as binary)", "input": "2c6bbc90d04d4d9d97", "output": "17d107b4417fee70349e1b5a4ddf152db2995d41e43a633927a427aef515aebc"}, {"method": "SHA256(variable2Hex as binary)", "input": "d04d4d9d", "output": "27e4b596ba32e7757a69c7a3f298bb44a7314b395d536fa940a683a2e88c9971"}]}, {"gameId": "acfd826c", "attempts": [{"method": "SHA256(constant)", "input": "4255143018", "output": "e7500adb39904571391df2c1c3395af421cb81c530ac7f0c152d8357b326e860"}, {"method": "SHA256(variable1)", "input": "125171500", "output": "752d0cc83741e60244b5890c8ac5be1195b7a25be1d170b67da87af4d52524ec"}, {"method": "SHA256(variable2)", "input": "3772698407", "output": "fff497d2c511aff778ed4ca7e636d471e188801f66e7e55b6f56ae95c979ef8d"}, {"method": "SHA256(lastByte)", "input": "158", "output": "7ed8f0f3b707956d9fb1e889e11153e0aa0a854983081d262fbe5eede32da7ca"}, {"method": "SHA256(constantHex)", "input": "6a54a0fd", "output": "8a5c729109e23d38499464e09905af0537167af6831fc2f6f79c145aadd12b0f"}, {"method": "SHA256(variable1Hex)", "input": "2cf77507", "output": "feb7221060565a58c2d2cbba350458a1551797c652039894fa4ef1951995b289"}, {"method": "SHA256(variable2Hex)", "input": "27cfdee0", "output": "0e9e3956615cc72a04f9ef9fa779cd28ec9f3ad03e59a748b72e7ac7e28be2e2"}, {"method": "SHA256(lastByteHex)", "input": "9e", "output": "1df697c51d78b5ec8221f24544a18e838cb6eab01c0d606b29b4407125962847"}, {"method": "SHA256(instructionData)", "input": "6a54a0fd2cf7750727cfdee09e", "output": "9d76b8345879bb243b0411aca49000aa1bc371a5849642cf872f9efdacae1a58"}, {"method": "SHA256(variablePartHex)", "input": "2cf7750727cfdee09e", "output": "265adbc00b6149a7bdf4117120f42d8917487d39ae401344be558c2bcb2170cb"}, {"method": "SHA256(fullData)", "input": "d80d78f2448503ab6a54a0fd2cf7750727cfdee09e", "output": "582d253d09f815d8ed426cc5e5cfe4a678bca7ba401fc4ffeb54151244462e6d"}, {"method": "SHA256(discriminator)", "input": "d80d78f2448503ab", "output": "4e86eecf63f21a005b6205748b906996a1bbe30e0d177391f192969b0ca7367a"}, {"method": "SHA256(constant + variable1)", "input": "4255143018125171500", "output": "63126b6d2e50bd81dcc77737f472707398e4f445d59e3fdebf805c78b73ffb56"}, {"method": "SHA256(constant + variable2)", "input": "42551430183772698407", "output": "bfa75309ee9565c5c3e6f02a5b93d4ca84250a74d11f4325e54e4dbed3134f1d"}, {"method": "SHA256(variable1 + variable2)", "input": "1251715003772698407", "output": "aa573c19215c02b6c81a6ef288a75f65c817886eb7ccdc975c8a72839d4fdb6f"}, {"method": "SHA256(variable1 + variable2 + lastByte)", "input": "1251715003772698407158", "output": "4a93e9ab946e60236ef5271024da85dc2cb739607c7bd6835c2198197dd8e8d5"}, {"method": "SHA256(constantHex + variable1Hex)", "input": "6a54a0fd2cf77507", "output": "f2d136d3687525d9a7f38d83f80710b9df8d3441c2bc518450251c2a53e8411b"}, {"method": "SHA256(constantHex + variable2Hex)", "input": "6a54a0fd27cfdee0", "output": "c8fe454cabb0d67176e563eb38578e2f86c3729fd863e3913bb3acec841ec9db"}, {"method": "SHA256(variable1Hex + variable2Hex)", "input": "2cf7750727cfdee0", "output": "a94a08657a8862e9bf0f0ba765e27dff7a1c98bb37edb6d8d05b7e5b10485cb8"}, {"method": "SHA256(variable1Hex + variable2Hex + lastByteHex)", "input": "2cf7750727cfdee09e", "output": "265adbc00b6149a7bdf4117120f42d8917487d39ae401344be558c2bcb2170cb"}, {"method": "SHA256(all variables concatenated)", "input": "1251715003772698407158", "output": "4a93e9ab946e60236ef5271024da85dc2cb739607c7bd6835c2198197dd8e8d5"}, {"method": "SHA256(all hex concatenated)", "input": "2cf7750727cfdee09e", "output": "265adbc00b6149a7bdf4117120f42d8917487d39ae401344be558c2bcb2170cb"}, {"method": "SHA256(instructionData + gameId)", "input": "6a54a0fd2cf7750727cfdee09eacfd826c...", "output": "f42f70eba756688ea177820623011a79334df2f89468715b3708c95e8a9da591"}, {"method": "SHA256(instructionData + fullGameId)", "input": "6a54a0fd2cf7750727cfdee09eacfd826c-c05e-4be0-80a4-f1c85b6f37bf...", "output": "8ae10ded941d3f4ee90c3c3f810c3e8bbc6067623024a881ec6a499df327c547"}, {"method": "SHA256(variable2Hex + gameId)", "input": "27cfdee0acfd826c...", "output": "72d4cf47391420767f21150fd36ac58b13a24d009a71e02d630dd3c058877067"}, {"method": "SHA256(variable2Hex + fullGameId)", "input": "27cfdee0acfd826c-c05e-4be0-80a4-f1c85b6f37bf...", "output": "40ae19bc3bacabb9bb4956c174e078c2c3fff96ef09960f888b1fa94a3a0bb91"}, {"method": "SHA256(variablePartHex + gameId)", "input": "2cf7750727cfdee09eacfd826c...", "output": "8f395898a1c93365f785d66c2da43253ce5d3cf669c277693e4b29ccdf4a7044"}, {"method": "SHA256(variablePartHex + fullGameId)", "input": "2cf7750727cfdee09eacfd826c-c05e-4be0-80a4-f1c85b6f37bf...", "output": "a42a06c35302ffcb796b523d01dbbbbd02b26233ca9a06625113cefc4a7f981c"}, {"method": "SHA256(instructionData + blockTime)", "input": "6a54a0fd2cf7750727cfdee09e1750748769...", "output": "5fbfee68174738a56cde5300050674e297adb19fbc865c0d6291c01971296049"}, {"method": "SHA256(instructionData + slot)", "input": "6a54a0fd2cf7750727cfdee09e348842874...", "output": "063507fed3fec07030f97a8bb0d908e66935b4474733d0f3d2b55932fcca965f"}, {"method": "SHA256(instructionData + signature)", "input": "6a54a0fd2cf7750727cfdee09eRxJe6NHPeD4CHwzytYGCg4Er79Scdk4xysbwa8HbGfXzNVjoLcz7oJZvxQZyN5Q4BGgrgNvThF...", "output": "196b51f7cc1a9f29947659750ed573069a05d2eb23096c8cb071bf04452d64fc"}, {"method": "SHA256(variable2 + blockTime)", "input": "37726984071750748769...", "output": "77a5e3520b6292015e1175201a52f1e0ac471c0898590919ba4f569599465642"}, {"method": "SHA256(variable2 + slot)", "input": "3772698407348842874...", "output": "b8af0a0c71504800385844a844cf5061bf7c1543dc2093e8b382e0039d4c060e"}, {"method": "MD5(instructionData)", "input": "6a54a0fd2cf7750727cfdee09e", "output": "85ff2e88bd6cfcc179bfa0f4f4a9c6b3"}, {"method": "SHA1(instructionData)", "input": "6a54a0fd2cf7750727cfdee09e", "output": "bddecaf1478e50279d377b3b4a0e523fea4940c4"}, {"method": "SHA256(instructionData)", "input": "6a54a0fd2cf7750727cfdee09e", "output": "dbd4c8e0e32d0a9f7cec8eb381396398d489b2ac2005cea3031ea205a1ecdb4c"}, {"method": "SHA512(instructionData)", "input": "6a54a0fd2cf7750727cfdee09e", "output": "d3076f818445666a06a57afa575873493facea34190ae0e1ae7bc5374a5803b149f8c05fea11ffc4c425049b665761b34f1099b1de3482e225bf79ddaf4c0ad6"}, {"method": "HMAC-SHA256(instructionData, \"acfd826c\")", "input": "6a54a0fd2cf7750727cfdee09e", "output": "2e09c0bc16a94c21925d8d788756cfd0e19bb8c033f732b5c7d2723fbd7d3494"}, {"method": "HMAC-SHA256(instructionData, \"acfd826c-c05e-4be0-80a4-f1c85b6f37bf\")", "input": "6a54a0fd2cf7750727cfdee09e", "output": "130945a3b90772aca573ccbea1922e07dcb89f8bf5981c43fbb73f6a982c9ce8"}, {"method": "HMAC-SHA256(instructionData, \"6a54a0fd\")", "input": "6a54a0fd2cf7750727cfdee09e", "output": "403e4ed0030a41e97bf5353052076f74ca908a26ef66ca6b5f138dc73b485a1b"}, {"method": "HMAC-SHA256(instructionData, \"d80d78f2448503ab\")", "input": "6a54a0fd2cf7750727cfdee09e", "output": "2218958780aac0bb6d9fa53ffd2934022d60513622508b8126ea569134e3b144"}, {"method": "HMAC-SHA256(instructionData, \"solpump\")", "input": "6a54a0fd2cf7750727cfdee09e", "output": "40e24deca70ccfc7dfb48de047ad19d3a30f1a9b0741dfdd612a1704b6b53fe2"}, {"method": "HMAC-SHA256(instructionData, \"crash\")", "input": "6a54a0fd2cf7750727cfdee09e", "output": "7497504b9419a747022c6d91baa1381e6c3debc4076db6404995f42679779814"}, {"method": "HMAC-SHA256(instructionData, \"game\")", "input": "6a54a0fd2cf7750727cfdee09e", "output": "a1e1355bd9e52d6ee3ab615cd4d286e6d699d949a034c0d097bdf5a40c4fa871"}, {"method": "SHA256(instructionData as binary)", "input": "6a54a0fd2cf7750727cfdee09e", "output": "dbd4c8e0e32d0a9f7cec8eb381396398d489b2ac2005cea3031ea205a1ecdb4c"}, {"method": "SHA256(variablePartHex as binary)", "input": "2cf7750727cfdee09e", "output": "71712fd20adb205ef9530930e3a283612881acdd312811beb6b55779d7d4f6cf"}, {"method": "SHA256(variable2Hex as binary)", "input": "27cfdee0", "output": "0c285db70c2cbbde9994dd7565c4e44d4812024e15c21c36df99721bd474ad27"}]}, {"gameId": "ad84e15d", "attempts": [{"method": "SHA256(constant)", "input": "4255143018", "output": "e7500adb39904571391df2c1c3395af421cb81c530ac7f0c152d8357b326e860"}, {"method": "SHA256(variable1)", "input": "3447125804", "output": "6282b9bde3ebf338b8834c18d737be2987a95a3e99a9fe76125cdd36a0c0a6ea"}, {"method": "SHA256(variable2)", "input": "844705590", "output": "5811ac7de949166c7c7f6ac6efe93e7427a510f3e6bb35f54d6eab8f8e2865c5"}, {"method": "SHA256(lastByte)", "input": "240", "output": "6af1f692e9496c6d0b668316eccb93276ae6b6774fa728aac31ff40a38318760"}, {"method": "SHA256(constantHex)", "input": "6a54a0fd", "output": "8a5c729109e23d38499464e09905af0537167af6831fc2f6f79c145aadd12b0f"}, {"method": "SHA256(variable1Hex)", "input": "2cf776cd", "output": "30b5083ce1e8f63dd8f89e545d31d4e7c2ad8173dc0d74cdbe8b4da8bc83a57e"}, {"method": "SHA256(variable2Hex)", "input": "362f5932", "output": "4f579c8c620b55d8e9884c59fdcb32143c8c632562e447f3f1191d88b3dcb48f"}, {"method": "SHA256(lastByteHex)", "input": "f0", "output": "865ab0d317f36965e43d20d275b545a6773137adad19db1d61ecb8032f473e0b"}, {"method": "SHA256(instructionData)", "input": "6a54a0fd2cf776cd362f5932f0", "output": "8eb803d0413d52bc2880ab0036aab83bb4f3930ba89ff9fb6ee05c1e700cd7db"}, {"method": "SHA256(variablePartHex)", "input": "2cf776cd362f5932f0", "output": "769ea20fafb85a37a8a518826f306edd6a148776298662b6a5a129cef7b0d892"}, {"method": "SHA256(fullData)", "input": "d80d78f2448503ab6a54a0fd2cf776cd362f5932f0", "output": "3fe7fa2d1c86658f7f366276a6839751fc2bc3b5eac20c5f0995bbee61672a0e"}, {"method": "SHA256(discriminator)", "input": "d80d78f2448503ab", "output": "4e86eecf63f21a005b6205748b906996a1bbe30e0d177391f192969b0ca7367a"}, {"method": "SHA256(constant + variable1)", "input": "42551430183447125804", "output": "3fc436d912277ff2d74ea710e483e7499ee8c17c6e4b57ebf7210b48414107e4"}, {"method": "SHA256(constant + variable2)", "input": "4255143018844705590", "output": "f05ec5cb3faf0973b59057a62acbb7db877719b833dcd6f8a9ed6d7f85612898"}, {"method": "SHA256(variable1 + variable2)", "input": "3447125804844705590", "output": "1ad0901f722819f063710582d9d19d8b04e92515176a1225d4066ff1f58ed0ae"}, {"method": "SHA256(variable1 + variable2 + lastByte)", "input": "3447125804844705590240", "output": "cd11f9f87018e2dcc5967f2f3ddff512287c50618bc3f65ede407a1f6bdaab48"}, {"method": "SHA256(constantHex + variable1Hex)", "input": "6a54a0fd2cf776cd", "output": "557560328faa3e356f9ffc43d9ea2c559a827011cf5807114e0919365720e468"}, {"method": "SHA256(constantHex + variable2Hex)", "input": "6a54a0fd362f5932", "output": "740e868baf221cb15ecd12323b5caa36f974b52c7e2bfda4e1df584e26e7d941"}, {"method": "SHA256(variable1Hex + variable2Hex)", "input": "2cf776cd362f5932", "output": "efc94f6443240d557f13c188707d35797c0d5de747b380b52aa06ac4eef4cbe2"}, {"method": "SHA256(variable1Hex + variable2Hex + lastByteHex)", "input": "2cf776cd362f5932f0", "output": "769ea20fafb85a37a8a518826f306edd6a148776298662b6a5a129cef7b0d892"}, {"method": "SHA256(all variables concatenated)", "input": "3447125804844705590240", "output": "cd11f9f87018e2dcc5967f2f3ddff512287c50618bc3f65ede407a1f6bdaab48"}, {"method": "SHA256(all hex concatenated)", "input": "2cf776cd362f5932f0", "output": "769ea20fafb85a37a8a518826f306edd6a148776298662b6a5a129cef7b0d892"}, {"method": "SHA256(instructionData + gameId)", "input": "6a54a0fd2cf776cd362f5932f0ad84e15d...", "output": "404c9a1038dd4a0042ea4b96025fb1aec6c56791ace699f8b8ad8c7d99f5c970"}, {"method": "SHA256(instructionData + fullGameId)", "input": "6a54a0fd2cf776cd362f5932f0ad84e15d-643a-4d67-8f26-0f91c86a2d13...", "output": "d5888ddf101205bd5a0b755d5e9e56e74773a95376edf2acf979c0bf52e6d173"}, {"method": "SHA256(variable2Hex + gameId)", "input": "362f5932ad84e15d...", "output": "95ade7798e2eb3350fc1e205347d35813eae8abdbe87f7f8acf4a16cbe2bad74"}, {"method": "SHA256(variable2Hex + fullGameId)", "input": "362f5932ad84e15d-643a-4d67-8f26-0f91c86a2d13...", "output": "73f39b2c4cc543945d9449d50cc87de944e2b8f09b47b905bec75c1f628b0150"}, {"method": "SHA256(variablePartHex + gameId)", "input": "2cf776cd362f5932f0ad84e15d...", "output": "bd96f9daf110a45739a25a420d91b43e39f994b8ad858e819d33db6ca5ec4fdb"}, {"method": "SHA256(variablePartHex + fullGameId)", "input": "2cf776cd362f5932f0ad84e15d-643a-4d67-8f26-0f91c86a2d13...", "output": "ee899017695f30dd9a7120ac5df2c66c7373ff00d69d7a70b6af9f3cc17cd5af"}, {"method": "SHA256(instructionData + blockTime)", "input": "6a54a0fd2cf776cd362f5932f01750748706...", "output": "b27c7a2db85944675b8597c3cb7182f721b369db59a7d88a309ca2c691c93592"}, {"method": "SHA256(instructionData + slot)", "input": "6a54a0fd2cf776cd362f5932f0348842720...", "output": "dcadc8ac88704d8049ced61474778884f5e71508a6cccd7160b1459809ae808a"}, {"method": "SHA256(instructionData + signature)", "input": "6a54a0fd2cf776cd362f5932f03UMr5jeVvzEpfHteeyjdEhRFtfBnrwBT7p6ttbmKGuND12zCWTWzkrsyLmie9Ex2nu7V1ei8F5...", "output": "96ac3bdd8fbe344e3095c4d5fb7b88a7cf5c7f2533558a32318868497358381a"}, {"method": "SHA256(variable2 + blockTime)", "input": "8447055901750748706...", "output": "ee9c9bc5c1d6afed33c10d44fc54931bf88b8ff570f5274b7936c5ba91ca5da5"}, {"method": "SHA256(variable2 + slot)", "input": "844705590348842720...", "output": "c81344a84bd6358d77f0e7c5e20814068f3188500e1bcb082c3774e31619aab5"}, {"method": "MD5(instructionData)", "input": "6a54a0fd2cf776cd362f5932f0", "output": "f375e15c988988d7c580c869eb025d64"}, {"method": "SHA1(instructionData)", "input": "6a54a0fd2cf776cd362f5932f0", "output": "56d0f619cc1d49f7c5889d66482d615644f02c75"}, {"method": "SHA256(instructionData)", "input": "6a54a0fd2cf776cd362f5932f0", "output": "b5be74a16673000fc4f390f72d648fa940c2d984fb9dba6a2060522b0ab65ee4"}, {"method": "SHA512(instructionData)", "input": "6a54a0fd2cf776cd362f5932f0", "output": "e88036cbbfd3171fd4eb2af73d4a650d16c4cb0015c34cb52987f6ece4c505fc96cb2381cd88951c5c3f9ed67e30c568904bc09707591b043c636044776b898d"}, {"method": "HMAC-SHA256(instructionData, \"ad84e15d\")", "input": "6a54a0fd2cf776cd362f5932f0", "output": "62dac7de42a013148841020f762d68a7635103c666d7606f49620e9f0052d3b8"}, {"method": "HMAC-SHA256(instructionData, \"ad84e15d-643a-4d67-8f26-0f91c86a2d13\")", "input": "6a54a0fd2cf776cd362f5932f0", "output": "6dd16e5a9777456db1239a33cfc8a282490c9876081f39f61c8a9e74b9445501"}, {"method": "HMAC-SHA256(instructionData, \"6a54a0fd\")", "input": "6a54a0fd2cf776cd362f5932f0", "output": "fbc21c06e047c3e8208d751f7168b90e6712b8b90b20c9261748d4dffbe7bd0b"}, {"method": "HMAC-SHA256(instructionData, \"d80d78f2448503ab\")", "input": "6a54a0fd2cf776cd362f5932f0", "output": "3692f0ffcd2808ae9387d78530454076993794e4a2cf3c606189dec0f9c0eb99"}, {"method": "HMAC-SHA256(instructionData, \"solpump\")", "input": "6a54a0fd2cf776cd362f5932f0", "output": "5e7eced2ae1991175e43557c2c32c7cd9abafb1207ef4a940bae63b6d7feb163"}, {"method": "HMAC-SHA256(instructionData, \"crash\")", "input": "6a54a0fd2cf776cd362f5932f0", "output": "2060fbe3c1ab180b26f60892032550e8140af15d31ee7c97a6a0884a5bf370bd"}, {"method": "HMAC-SHA256(instructionData, \"game\")", "input": "6a54a0fd2cf776cd362f5932f0", "output": "1061b08eee888255feccff680852edf55ddc3541a22ff53ae848a677d8bfe1e4"}, {"method": "SHA256(instructionData as binary)", "input": "6a54a0fd2cf776cd362f5932f0", "output": "b5be74a16673000fc4f390f72d648fa940c2d984fb9dba6a2060522b0ab65ee4"}, {"method": "SHA256(variablePartHex as binary)", "input": "2cf776cd362f5932f0", "output": "1f930695eae589227308c9565cba5d36b002297bd98215f0357d86f75d6ab1be"}, {"method": "SHA256(variable2Hex as binary)", "input": "362f5932", "output": "42cef3f06beb5d866d10783d0e8942c5cea84e1ef765248524ad3cdaed9727ff"}]}], "method_counts": {"SHA256(constant)": ["e7500adb39904571391df2c1c3395af421cb81c530ac7f0c152d8357b326e860", "e7500adb39904571391df2c1c3395af421cb81c530ac7f0c152d8357b326e860", "e7500adb39904571391df2c1c3395af421cb81c530ac7f0c152d8357b326e860"], "SHA256(variable1)": ["b43f283325f277eb5ffd77bbc37c47cbaf61ef020380b94c52ea3cbbcab1d3f5", "752d0cc83741e60244b5890c8ac5be1195b7a25be1d170b67da87af4d52524ec", "6282b9bde3ebf338b8834c18d737be2987a95a3e99a9fe76125cdd36a0c0a6ea"], "SHA256(variable2)": ["1639b13e3fc71777a7aba162a4425da8ce047ea9a9f75552db595a141f3b0566", "fff497d2c511aff778ed4ca7e636d471e188801f66e7e55b6f56ae95c979ef8d", "5811ac7de949166c7c7f6ac6efe93e7427a510f3e6bb35f54d6eab8f8e2865c5"], "SHA256(lastByte)": ["8e612bd1f5d132a339575b8dafb7842c64614e56bcf3d5ab65a0bc4b34329407", "7ed8f0f3b707956d9fb1e889e11153e0aa0a854983081d262fbe5eede32da7ca", "6af1f692e9496c6d0b668316eccb93276ae6b6774fa728aac31ff40a38318760"], "SHA256(constantHex)": ["8a5c729109e23d38499464e09905af0537167af6831fc2f6f79c145aadd12b0f", "8a5c729109e23d38499464e09905af0537167af6831fc2f6f79c145aadd12b0f", "8a5c729109e23d38499464e09905af0537167af6831fc2f6f79c145aadd12b0f"], "SHA256(variable1Hex)": ["3058501369e38598a53328511c4bb8497f209897071b55248170bcac3d08031b", "feb7221060565a58c2d2cbba350458a1551797c652039894fa4ef1951995b289", "30b5083ce1e8f63dd8f89e545d31d4e7c2ad8173dc0d74cdbe8b4da8bc83a57e"], "SHA256(variable2Hex)": ["c5bea19b8477b39fc5bad6a7f139e2c12409b1f9744c1933270fa86eb4a57454", "0e9e3956615cc72a04f9ef9fa779cd28ec9f3ad03e59a748b72e7ac7e28be2e2", "4f579c8c620b55d8e9884c59fdcb32143c8c632562e447f3f1191d88b3dcb48f"], "SHA256(lastByteHex)": ["d6d824abba4afde81129c71dea75b8100e96338da5f416d2f69088f1960cb091", "1df697c51d78b5ec8221f24544a18e838cb6eab01c0d606b29b4407125962847", "865ab0d317f36965e43d20d275b545a6773137adad19db1d61ecb8032f473e0b"], "SHA256(instructionData)": ["776cb0347bb8646db8ecedc9de796e8a88ab79cc8998fc5fd425963640a44187", "bd7ca163811f733c4fd4ad4a83d031faa3ffb6aba2fdd7af4f0825aedb67ac5d", "9d76b8345879bb243b0411aca49000aa1bc371a5849642cf872f9efdacae1a58", "dbd4c8e0e32d0a9f7cec8eb381396398d489b2ac2005cea3031ea205a1ecdb4c", "8eb803d0413d52bc2880ab0036aab83bb4f3930ba89ff9fb6ee05c1e700cd7db", "b5be74a16673000fc4f390f72d648fa940c2d984fb9dba6a2060522b0ab65ee4"], "SHA256(variablePartHex)": ["ea8e8d4544af0fad4b114d0cfe2920c25caaa189ac30bea66c58398b3e273579", "265adbc00b6149a7bdf4117120f42d8917487d39ae401344be558c2bcb2170cb", "769ea20fafb85a37a8a518826f306edd6a148776298662b6a5a129cef7b0d892"], "SHA256(fullData)": ["e52dbe4104a07de55f698586077f268005a083226a70240e7e4d570e8dbd5a90", "582d253d09f815d8ed426cc5e5cfe4a678bca7ba401fc4ffeb54151244462e6d", "3fe7fa2d1c86658f7f366276a6839751fc2bc3b5eac20c5f0995bbee61672a0e"], "SHA256(discriminator)": ["4e86eecf63f21a005b6205748b906996a1bbe30e0d177391f192969b0ca7367a", "4e86eecf63f21a005b6205748b906996a1bbe30e0d177391f192969b0ca7367a", "4e86eecf63f21a005b6205748b906996a1bbe30e0d177391f192969b0ca7367a"], "SHA256(constant + variable1)": ["a4f38057b6ad4694fbe2a3570a41fa60accd77c40147c27520678b070fa80ca2", "63126b6d2e50bd81dcc77737f472707398e4f445d59e3fdebf805c78b73ffb56", "3fc436d912277ff2d74ea710e483e7499ee8c17c6e4b57ebf7210b48414107e4"], "SHA256(constant + variable2)": ["bd2ef2e11a871509821b164968b89448a2061ec2c61b0dc70009e2b5181afce2", "bfa75309ee9565c5c3e6f02a5b93d4ca84250a74d11f4325e54e4dbed3134f1d", "f05ec5cb3faf0973b59057a62acbb7db877719b833dcd6f8a9ed6d7f85612898"], "SHA256(variable1 + variable2)": ["a3a5b9ae63848ca4c9d5f8cd4935e8ba21a141c4bb5ffede39fab8339d4eb869", "aa573c19215c02b6c81a6ef288a75f65c817886eb7ccdc975c8a72839d4fdb6f", "1ad0901f722819f063710582d9d19d8b04e92515176a1225d4066ff1f58ed0ae"], "SHA256(variable1 + variable2 + lastByte)": ["27f2f93d7820ab4e6b0aae17fde2a939d34bfee4e513628bcce0e94723cd2674", "4a93e9ab946e60236ef5271024da85dc2cb739607c7bd6835c2198197dd8e8d5", "cd11f9f87018e2dcc5967f2f3ddff512287c50618bc3f65ede407a1f6bdaab48"], "SHA256(constantHex + variable1Hex)": ["4405dec07fb1545365c60f52f13775793cdec9ea08ead17020dfc118dd058a79", "f2d136d3687525d9a7f38d83f80710b9df8d3441c2bc518450251c2a53e8411b", "557560328faa3e356f9ffc43d9ea2c559a827011cf5807114e0919365720e468"], "SHA256(constantHex + variable2Hex)": ["a164c9f9784ebdd124842b41c12ec38598e22dcd121a3e7f8c27c1bac5aedf92", "c8fe454cabb0d67176e563eb38578e2f86c3729fd863e3913bb3acec841ec9db", "740e868baf221cb15ecd12323b5caa36f974b52c7e2bfda4e1df584e26e7d941"], "SHA256(variable1Hex + variable2Hex)": ["c89850df7dd3b59de68b06172e4580fe339bbcb41d854de8278ef0cf3df339f3", "a94a08657a8862e9bf0f0ba765e27dff7a1c98bb37edb6d8d05b7e5b10485cb8", "efc94f6443240d557f13c188707d35797c0d5de747b380b52aa06ac4eef4cbe2"], "SHA256(variable1Hex + variable2Hex + lastByteHex)": ["ea8e8d4544af0fad4b114d0cfe2920c25caaa189ac30bea66c58398b3e273579", "265adbc00b6149a7bdf4117120f42d8917487d39ae401344be558c2bcb2170cb", "769ea20fafb85a37a8a518826f306edd6a148776298662b6a5a129cef7b0d892"], "SHA256(all variables concatenated)": ["27f2f93d7820ab4e6b0aae17fde2a939d34bfee4e513628bcce0e94723cd2674", "4a93e9ab946e60236ef5271024da85dc2cb739607c7bd6835c2198197dd8e8d5", "cd11f9f87018e2dcc5967f2f3ddff512287c50618bc3f65ede407a1f6bdaab48"], "SHA256(all hex concatenated)": ["ea8e8d4544af0fad4b114d0cfe2920c25caaa189ac30bea66c58398b3e273579", "265adbc00b6149a7bdf4117120f42d8917487d39ae401344be558c2bcb2170cb", "769ea20fafb85a37a8a518826f306edd6a148776298662b6a5a129cef7b0d892"], "SHA256(instructionData + gameId)": ["a3ff9aa18af060acaf8ae248fbd8209a5e5ce6ffa5afe1a111c0cd51588e971f", "f42f70eba756688ea177820623011a79334df2f89468715b3708c95e8a9da591", "404c9a1038dd4a0042ea4b96025fb1aec6c56791ace699f8b8ad8c7d99f5c970"], "SHA256(instructionData + fullGameId)": ["561e8fc8bf1cf7d729cbd48ed2c343d77aab20d7daed462680a6acf94f25b3ca", "8ae10ded941d3f4ee90c3c3f810c3e8bbc6067623024a881ec6a499df327c547", "d5888ddf101205bd5a0b755d5e9e56e74773a95376edf2acf979c0bf52e6d173"], "SHA256(variable2Hex + gameId)": ["c21c6b3d867a9cf7f0580bdb84ca399ef723158f4079bf53c4a31188f63b988a", "72d4cf47391420767f21150fd36ac58b13a24d009a71e02d630dd3c058877067", "95ade7798e2eb3350fc1e205347d35813eae8abdbe87f7f8acf4a16cbe2bad74"], "SHA256(variable2Hex + fullGameId)": ["e8fd03efdcc2d1a897a7cd3c14f533bef4e4a76a4565e87710fe3d0ae91d3a6a", "40ae19bc3bacabb9bb4956c174e078c2c3fff96ef09960f888b1fa94a3a0bb91", "73f39b2c4cc543945d9449d50cc87de944e2b8f09b47b905bec75c1f628b0150"], "SHA256(variablePartHex + gameId)": ["0e4e1e15a448818a801312b8afd4b40aaead2b25178cd09c6aebc6c1527ae11e", "8f395898a1c93365f785d66c2da43253ce5d3cf669c277693e4b29ccdf4a7044", "bd96f9daf110a45739a25a420d91b43e39f994b8ad858e819d33db6ca5ec4fdb"], "SHA256(variablePartHex + fullGameId)": ["7e88678e4131895370eaa33888cdff3d398ee44765e61363354b8bf15dcfe694", "a42a06c35302ffcb796b523d01dbbbbd02b26233ca9a06625113cefc4a7f981c", "ee899017695f30dd9a7120ac5df2c66c7373ff00d69d7a70b6af9f3cc17cd5af"], "SHA256(instructionData + blockTime)": ["2eea06a02c418c3af347a75bb319b999ba458c15c6023cf4f6f64d0b48829f6b", "5fbfee68174738a56cde5300050674e297adb19fbc865c0d6291c01971296049", "b27c7a2db85944675b8597c3cb7182f721b369db59a7d88a309ca2c691c93592"], "SHA256(instructionData + slot)": ["2887aadf3dff38064c94f079dd17141393d563ac9d471d706cd1ff831227b477", "063507fed3fec07030f97a8bb0d908e66935b4474733d0f3d2b55932fcca965f", "dcadc8ac88704d8049ced61474778884f5e71508a6cccd7160b1459809ae808a"], "SHA256(instructionData + signature)": ["4d7db6c9bc5f6e0ab812f90589533cb1c8d3002ca7ddac7cb5346d4e4010fc62", "196b51f7cc1a9f29947659750ed573069a05d2eb23096c8cb071bf04452d64fc", "96ac3bdd8fbe344e3095c4d5fb7b88a7cf5c7f2533558a32318868497358381a"], "SHA256(variable2 + blockTime)": ["79a2861110bb49341c7893d81f74486877f7560ef36f56b181b8f8581c64d035", "77a5e3520b6292015e1175201a52f1e0ac471c0898590919ba4f569599465642", "ee9c9bc5c1d6afed33c10d44fc54931bf88b8ff570f5274b7936c5ba91ca5da5"], "SHA256(variable2 + slot)": ["718ad26f9392d01db85503109e4b5cee666d44156fa09cc083ace1b084d2b1b4", "b8af0a0c71504800385844a844cf5061bf7c1543dc2093e8b382e0039d4c060e", "c81344a84bd6358d77f0e7c5e20814068f3188500e1bcb082c3774e31619aab5"], "MD5(instructionData)": ["a46e37906e812e4c508aef7f94e7d424", "85ff2e88bd6cfcc179bfa0f4f4a9c6b3", "f375e15c988988d7c580c869eb025d64"], "SHA1(instructionData)": ["917aa95f26f5b605687227c2583d234ed8364dbf", "bddecaf1478e50279d377b3b4a0e523fea4940c4", "56d0f619cc1d49f7c5889d66482d615644f02c75"], "SHA512(instructionData)": ["b450b7854a30e12cd077a05b7de0978b01333fe92da2d8e37283f13649c88596cabc764c3059bdc737dd950ffeb182e060a1c3f99713c45a9233d766fe6f527f", "d3076f818445666a06a57afa575873493facea34190ae0e1ae7bc5374a5803b149f8c05fea11ffc4c425049b665761b34f1099b1de3482e225bf79ddaf4c0ad6", "e88036cbbfd3171fd4eb2af73d4a650d16c4cb0015c34cb52987f6ece4c505fc96cb2381cd88951c5c3f9ed67e30c568904bc09707591b043c636044776b898d"], "HMAC-SHA256(instructionData, \"93be55a8\")": ["32f4f32c29a8ef363499b1e486a58090d4239e8734435127d7a12cd18829c630"], "HMAC-SHA256(instructionData, \"93be55a8-b790-4fad-87bb-2900eb874415\")": ["41a18c97043c554001bf0e68bb51cb55026393f6c751cad218760fde23ab7b6f"], "HMAC-SHA256(instructionData, \"6a54a0fd\")": ["96af3a84616f27258cdcc8820abb9a8b0a2aac4c435e3f744f6264f5f2853c7a", "403e4ed0030a41e97bf5353052076f74ca908a26ef66ca6b5f138dc73b485a1b", "fbc21c06e047c3e8208d751f7168b90e6712b8b90b20c9261748d4dffbe7bd0b"], "HMAC-SHA256(instructionData, \"d80d78f2448503ab\")": ["edb6bd21c137880242d1f426df278e5d3b21b40040c1a208803925a0b28d8d4e", "2218958780aac0bb6d9fa53ffd2934022d60513622508b8126ea569134e3b144", "3692f0ffcd2808ae9387d78530454076993794e4a2cf3c606189dec0f9c0eb99"], "HMAC-SHA256(instructionData, \"solpump\")": ["d801bf87b3be1899996674fc9c11e46eb2863a30f3c1d09ce05dd5099fb3f0e0", "40e24deca70ccfc7dfb48de047ad19d3a30f1a9b0741dfdd612a1704b6b53fe2", "5e7eced2ae1991175e43557c2c32c7cd9abafb1207ef4a940bae63b6d7feb163"], "HMAC-SHA256(instructionData, \"crash\")": ["afcbdd6b44c099538c93bf9d015c03bc60f1ca1486582741c7e87fe0ca2bcd34", "7497504b9419a747022c6d91baa1381e6c3debc4076db6404995f42679779814", "2060fbe3c1ab180b26f60892032550e8140af15d31ee7c97a6a0884a5bf370bd"], "HMAC-SHA256(instructionData, \"game\")": ["c537e6fba2fba52c6b0de4adba639ea50f44b1c368f2af8a03f3a6c7aef699d7", "a1e1355bd9e52d6ee3ab615cd4d286e6d699d949a034c0d097bdf5a40c4fa871", "1061b08eee888255feccff680852edf55ddc3541a22ff53ae848a677d8bfe1e4"], "SHA256(instructionData as binary)": ["bd7ca163811f733c4fd4ad4a83d031faa3ffb6aba2fdd7af4f0825aedb67ac5d", "dbd4c8e0e32d0a9f7cec8eb381396398d489b2ac2005cea3031ea205a1ecdb4c", "b5be74a16673000fc4f390f72d648fa940c2d984fb9dba6a2060522b0ab65ee4"], "SHA256(variablePartHex as binary)": ["17d107b4417fee70349e1b5a4ddf152db2995d41e43a633927a427aef515aebc", "71712fd20adb205ef9530930e3a283612881acdd312811beb6b55779d7d4f6cf", "1f930695eae589227308c9565cba5d36b002297bd98215f0357d86f75d6ab1be"], "SHA256(variable2Hex as binary)": ["27e4b596ba32e7757a69c7a3f298bb44a7314b395d536fa940a683a2e88c9971", "0c285db70c2cbbde9994dd7565c4e44d4812024e15c21c36df99721bd474ad27", "42cef3f06beb5d866d10783d0e8942c5cea84e1ef765248524ad3cdaed9727ff"], "HMAC-SHA256(instructionData, \"acfd826c\")": ["2e09c0bc16a94c21925d8d788756cfd0e19bb8c033f732b5c7d2723fbd7d3494"], "HMAC-SHA256(instructionData, \"acfd826c-c05e-4be0-80a4-f1c85b6f37bf\")": ["130945a3b90772aca573ccbea1922e07dcb89f8bf5981c43fbb73f6a982c9ce8"], "HMAC-SHA256(instructionData, \"ad84e15d\")": ["62dac7de42a013148841020f762d68a7635103c666d7606f49620e9f0052d3b8"], "HMAC-SHA256(instructionData, \"ad84e15d-643a-4d67-8f26-0f91c86a2d13\")": ["6dd16e5a9777456db1239a33cfc8a282490c9876081f39f61c8a9e74b9445501"]}}