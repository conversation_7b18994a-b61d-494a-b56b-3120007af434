import fs from 'fs';
import { Buffer } from 'buffer';

/**
 * Clean summary of all possible values from CreateCrashRound instruction data
 */

// Load the complete game data
const gameData = JSON.parse(fs.readFileSync('./complete-games/complete-game-93be55a8.json', 'utf8'));

// Extract the CreateCrashRound data
const createStage = gameData.stages.CreateCrashRound;
const instruction = createStage.transaction.transaction.message.instructions.find(
  inst => inst.programId === '6YVBrao9DSLVGk7QsXxQVPbs4XyHVtHw9oSTTXw1otEW'
);

const buffer = Buffer.from(instruction.data, 'base64');
const data = buffer.slice(8); // The 13 bytes after discriminator

console.log('🎯 COMPLETE LIST OF ALL POSSIBLE VALUES FROM CreateCrashRound DATA');
console.log('================================================================');
console.log('Raw Data (13 bytes):', data.toString('hex'));
console.log('Raw Bytes:', Array.from(data));
console.log('');
console.log('Game Context:');
console.log('  Custom ID:', gameData.game_info.custom_id);
console.log('  Server Seed:', gameData.api_snapshots.EndCrashRound.serverSeed);
console.log('  Crash Multiplier:', gameData.api_snapshots.EndCrashRound.crashMultiplier);
console.log('  Block Time:', createStage.transaction.blockTime);
console.log('  Slot:', createStage.transaction.slot);

console.log('\n📊 ALL EXTRACTABLE VALUES:');
console.log('==========================');

// 1. Individual bytes
console.log('\n1️⃣ INDIVIDUAL BYTES (13 values):');
for (let i = 0; i < data.length; i++) {
  const byte = data[i];
  console.log(`  Byte ${i}: ${byte} (0x${byte.toString(16).padStart(2, '0')})`);
}

// 2. 16-bit values
console.log('\n2️⃣ 16-BIT INTEGERS (12 values):');
for (let i = 0; i <= data.length - 2; i++) {
  const u16 = data.readUInt16LE(i);
  console.log(`  u16[${i}]: ${u16} (0x${u16.toString(16)})`);
}

// 3. 32-bit values
console.log('\n3️⃣ 32-BIT INTEGERS (10 values):');
for (let i = 0; i <= data.length - 4; i++) {
  const u32 = data.readUInt32LE(i);
  const i32 = data.readInt32LE(i);
  console.log(`  u32[${i}]: ${u32} (0x${u32.toString(16)}) | signed: ${i32}`);
}

// 4. 64-bit values
console.log('\n4️⃣ 64-BIT INTEGERS (6 values):');
for (let i = 0; i <= data.length - 8; i++) {
  const u64 = data.readBigUInt64LE(i);
  console.log(`  u64[${i}]: ${u64.toString()} (0x${u64.toString(16)})`);
}

// 5. Floating point values
console.log('\n5️⃣ FLOATING POINT VALUES:');
for (let i = 0; i <= data.length - 4; i++) {
  const float = data.readFloatLE(i);
  console.log(`  float[${i}]: ${float}`);
}

for (let i = 0; i <= data.length - 8; i++) {
  const double = data.readDoubleLE(i);
  console.log(`  double[${i}]: ${double}`);
}

// 6. Hex sequences
console.log('\n6️⃣ HEX SEQUENCES:');
console.log('  4-byte sequences:');
for (let i = 0; i <= data.length - 4; i++) {
  const hex4 = data.slice(i, i + 4).toString('hex');
  console.log(`    [${i}-${i+3}]: ${hex4}`);
}

console.log('  8-byte sequences:');
for (let i = 0; i <= data.length - 8; i++) {
  const hex8 = data.slice(i, i + 8).toString('hex');
  console.log(`    [${i}-${i+7}]: ${hex8}`);
}

// 7. Structured interpretations
console.log('\n7️⃣ STRUCTURED INTERPRETATIONS:');

console.log('  Structure A: u32 + u64 + u8');
const structA = {
  field1: data.readUInt32LE(0),
  field2: data.readBigUInt64LE(4).toString(),
  field3: data[12]
};
console.log(`    Field 1 (u32): ${structA.field1}`);
console.log(`    Field 2 (u64): ${structA.field2}`);
console.log(`    Field 3 (u8): ${structA.field3}`);

console.log('  Structure B: u64 + u32 + u8');
const structB = {
  field1: data.readBigUInt64LE(0).toString(),
  field2: data.readUInt32LE(8),
  field3: data[12]
};
console.log(`    Field 1 (u64): ${structB.field1}`);
console.log(`    Field 2 (u32): ${structB.field2}`);
console.log(`    Field 3 (u8): ${structB.field3}`);

console.log('  Structure C: u32 + u32 + u32 + u8');
const structC = {
  field1: data.readUInt32LE(0),
  field2: data.readUInt32LE(4),
  field3: data.readUInt32LE(8),
  field4: data[12]
};
console.log(`    Field 1 (u32): ${structC.field1}`);
console.log(`    Field 2 (u32): ${structC.field2}`);
console.log(`    Field 3 (u32): ${structC.field3}`);
console.log(`    Field 4 (u8): ${structC.field4}`);

// 8. Most significant values summary
console.log('\n🎯 MOST SIGNIFICANT VALUES TO INVESTIGATE:');
console.log('==========================================');

const significantValues = [
  { name: 'First u32', value: data.readUInt32LE(0), hex: data.readUInt32LE(0).toString(16) },
  { name: 'Second u32', value: data.readUInt32LE(4), hex: data.readUInt32LE(4).toString(16) },
  { name: 'Third u32', value: data.readUInt32LE(8), hex: data.readUInt32LE(8).toString(16) },
  { name: 'First u64', value: data.readBigUInt64LE(0).toString(), hex: data.readBigUInt64LE(0).toString(16) },
  { name: 'Second u64', value: data.readBigUInt64LE(4).toString(), hex: data.readBigUInt64LE(4).toString(16) },
  { name: 'Last byte', value: data[12], hex: data[12].toString(16) },
  { name: 'Full hex', value: data.toString('hex'), hex: data.toString('hex') }
];

significantValues.forEach(val => {
  console.log(`${val.name}: ${val.value} (0x${val.hex})`);
});

console.log('\n💡 WHAT THESE VALUES MIGHT REPRESENT:');
console.log('=====================================');
console.log('1. Game initialization parameters');
console.log('2. Seed material for randomness generation');
console.log('3. Timestamp or block-related data');
console.log('4. Player or game session identifiers');
console.log('5. Cryptographic nonces or salts');
console.log('6. Game configuration settings');
console.log('7. Random values for fair play verification');

console.log('\n🔍 FOR REVERSE ENGINEERING, FOCUS ON:');
console.log('=====================================');
console.log('• The three 32-bit values: 4255143018, 2428267308, 2639089104');
console.log('• The two 64-bit values: 10429328678061102186, 11334801395338210092');
console.log('• The final byte: 151');
console.log('• How these might combine to influence the server seed or crash multiplier');
console.log('• Whether any of these values appear in other games');
console.log('• Patterns in how these values change between different games');

console.log('\n📋 COMPLETE VALUE INVENTORY:');
console.log('============================');
console.log('Total extractable values from 13 bytes:');
console.log('• 13 individual bytes');
console.log('• 12 16-bit integers');
console.log('• 10 32-bit integers');
console.log('• 6 64-bit integers');
console.log('• 10 32-bit floats');
console.log('• 6 64-bit doubles');
console.log('• Multiple hex sequences');
console.log('• 3 structured interpretations');
console.log('');
console.log('🎯 These are ALL the possible values that can be extracted from the CreateCrashRound instruction data!');
console.log('   Use these for correlation analysis with server seeds from multiple games.');
