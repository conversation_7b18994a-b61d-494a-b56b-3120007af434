{"game_info": {"custom_id": "93be55a8", "api_game_id": "93be55a8-b790-4fad-87bb-2900eb874415", "start_time": "2025-06-24T06:48:09.955Z", "completion_time": "2025-06-24T06:49:00.628Z", "is_complete": true}, "key_data": {"serverSeed": "58254f60f9661ee71d6c1514ec774914df925d9da9f5550524d43a12a0ed46db", "hashedServerSeed": "zjYhc3F9DEw/rSPAFuFICyfwOqAF0d1vQJb1dpWP/Ww=", "clientSeed": "WeF60TQOI5ton2ddRSUnfd+sDjgk4y/co8NDd+nXYZk=", "crashMultiplier": 3.72}, "transaction_data": {"CreateCrashRound": {"base64": "2A148kSFA6tqVKD9LGu8kNBNTZ2X", "hex": "d80d78f2448503ab6a54a0fd2c6bbc90d04d4d9d97", "bytes": [216, 13, 120, 242, 68, 133, 3, 171, 106, 84, 160, 253, 44, 107, 188, 144, 208, 77, 77, 157, 151], "length": 21, "signature": "2emHHBvZtnKL4buc4h3a4fpmuqUJBvwQEUp7uriT7muj8vraQM2e9TW83WPeeau3KkkLts2M9aBRLFp6oAZGCJD9", "blockTime": 1750747687, "slot": 348840187}, "StartCrashRound": {"base64": "44Yc2DLQ5xq", "hex": "e3861cd832d0e71a", "bytes": [227, 134, 28, 216, 50, 208, 231, 26], "length": 8, "signature": "4EJp32WZYqEcesXdQZ5NyXDdzwpdYMRGiA5cpXK32Jq3gLkoLL1aQGK7Sb9qnt1B4s3mdkasnQnAtiF6KKGtyjFZ", "blockTime": 1750747711, "slot": 348840243}, "EndCrashRound": {"base64": "fVvnPShDm2Q", "hex": "7d5be73d28439b64", "bytes": [125, 91, 231, 61, 40, 67, 155, 100], "length": 8, "signature": "3efdvTdkRLPtj3VBDR2FbEDwGY9d1nKR7VEGDkAWnZtUhHeyzTz22TFLR2vmbvem7HfEx7fPRQUCg5JDuZtDggu9", "blockTime": 1750747736, "slot": 348840305}}, "correlation_attempts": [{"method": "SHA256(CreateCrashRound_data)", "input": "d80d78f2448503ab6a54a0fd2c6bbc90d04d4d9d97", "output": "2585ce405f9af5cf47c69c9814463315f98af576aa35e25d0e51a98e418e9c25", "matches": false}, {"method": "SHA256(\"CreateCrashRound\" + CreateCrashRound_data)", "input": "CreateCrashRoundd80d78f2448503ab6a54a0fd2c6bbc90d04d4d9d97", "output": "bbf6deb0fc6e733c213d85c824f51311046fe0a8f67de06535b818e7e9edb5bd", "matches": false}, {"method": "SHA256(CreateCrashRound_data + signature)", "input": "d80d78f2448503ab6a54a0fd2c6bbc90d04d4d9d972emHHBvZtnKL4buc4h3a4fpmuqUJBvwQEUp7uriT7muj8vraQM2e9TW83WPeeau3KkkLts2M9aBRLFp6oAZGCJD9", "output": "921bacaaf3cffec7352be8ed47c68a3536041b88514a38b99dc1358b352ecd88", "matches": false}, {"method": "SHA256(CreateCrashRound_data + blockTime)", "input": "d80d78f2448503ab6a54a0fd2c6bbc90d04d4d9d97274a5a6800000000", "output": "21759444053de76bb541875cc054e46f5cf36327ac3908b083c0057bfe309c4c", "matches": false}, {"method": "SHA256(CreateCrashRound_data + slot)", "input": "d80d78f2448503ab6a54a0fd2c6bbc90d04d4d9d97fbe0ca1400000000", "output": "9456ec13771f19f131a9dcf27e6fcbd586ad6d7fa403371c4cdf40dc5b87e66c", "matches": false}, {"method": "SHA256(StartCrashRound_data)", "input": "e3861cd832d0e71a", "output": "d6aa9efc901e718481e81a793a9b611863dc0118e27970083870f81c03fa15da", "matches": false}, {"method": "SHA256(\"StartCrashRound\" + StartCrashRound_data)", "input": "StartCrashRounde3861cd832d0e71a", "output": "1d06296c36ac135344643d7ff215c5dbc7609c15902438ce1857b1cdd278c4e6", "matches": false}, {"method": "SHA256(StartCrashRound_data + signature)", "input": "e3861cd832d0e71a4EJp32WZYqEcesXdQZ5NyXDdzwpdYMRGiA5cpXK32Jq3gLkoLL1aQGK7Sb9qnt1B4s3mdkasnQnAtiF6KKGtyjFZ", "output": "4e2ede076edd512bf0a4ea7af27ec54399afdc0fbd85f4691c59b0422065ef03", "matches": false}, {"method": "SHA256(StartCrashRound_data + blockTime)", "input": "e3861cd832d0e71a3f4a5a6800000000", "output": "5a6b69876b4f528cc4fffc5e4a1f0e1ba3612297924ef627acbf963911e2ee13", "matches": false}, {"method": "SHA256(StartCrashRound_data + slot)", "input": "e3861cd832d0e71a33e1ca1400000000", "output": "6b553aa1e8a6a5ff917e0cc31036efbc338e80c985237606637de5b47624544b", "matches": false}, {"method": "SHA256(EndCrashRound_data)", "input": "7d5be73d28439b64", "output": "670616a53387e0bf0c5b762e3fcca7fffc330dc6c8f72d181cfdb4f78b4de21b", "matches": false}, {"method": "SHA256(\"EndCrashRound\" + EndCrashRound_data)", "input": "EndCrashRound7d5be73d28439b64", "output": "95fb0f6f40ca61479ddc2dd04d97d4bd816582dcf4f042725e084fa05ec3b6e1", "matches": false}, {"method": "SHA256(EndCrashRound_data + signature)", "input": "7d5be73d28439b643efdvTdkRLPtj3VBDR2FbEDwGY9d1nKR7VEGDkAWnZtUhHeyzTz22TFLR2vmbvem7HfEx7fPRQUCg5JDuZtDggu9", "output": "e4ce850cff1f41a5686b7f725d72aa51f9a5d1eada0fdde1ce0a362a432067ac", "matches": false}, {"method": "SHA256(EndCrashRound_data + blockTime)", "input": "7d5be73d28439b64584a5a6800000000", "output": "27a3f3725e5edb86133374bbd4a3a3820607434208edb698f001fcd3422ccecf", "matches": false}, {"method": "SHA256(EndCrashRound_data + slot)", "input": "7d5be73d28439b6471e1ca1400000000", "output": "f4ff76276b52c191fa66e4806b43df85d82d80b56c2e28ae92f110aa7a0d80ed", "matches": false}, {"method": "SHA256(all_stages_combined)", "input": "d80d78f2448503ab6a54a0fd2c6bbc90d04d4d9d97e3861cd832d0e71a7d5be73d28439b64", "output": "40842b423fa80a3702d24e0b17e3d7e82f6d71a38e8c78d8ffc03fd71fee5dbe", "matches": false}, {"method": "SHA256(CreateCrashRound_data + gameId)", "input": "d80d78f2448503ab6a54a0fd2c6bbc90d04d4d9d9793be55a8b7904fad87bb2900eb874415", "output": "5d76a6d242802c6a81089ca8151c138e08864fcb6eead1a42df2da6f57b90079", "matches": false}, {"method": "SHA256(StartCrashRound_data + gameId)", "input": "e3861cd832d0e71a93be55a8b7904fad87bb2900eb874415", "output": "494a5ee6aa425f17423826cfcdca17d8ec6a1902cd0d396d1bc71525457cc675", "matches": false}, {"method": "SHA256(EndCrashRound_data + gameId)", "input": "7d5be73d28439b6493be55a8b7904fad87bb2900eb874415", "output": "4a59ce886e24fa86372ec786fcdfa072dd0dc086c0bb167b5c1d40805d953560", "matches": false}, {"method": "SHA256(CreateCrashRound_data + customId)", "input": "d80d78f2448503ab6a54a0fd2c6bbc90d04d4d9d9793be55a8", "output": "e7a26bc4ac3f282045b868f506ca86d16bb158c95969c81cd9523370c29d0a31", "matches": false}, {"method": "SHA256(StartCrashRound_data + customId)", "input": "e3861cd832d0e71a93be55a8", "output": "f15ae22c80c9fa2058953f448a452d42a6674ad84122716f062f08b293c414bf", "matches": false}, {"method": "SHA256(EndCrashRound_data + customId)", "input": "7d5be73d28439b6493be55a8", "output": "8d1288ebf0293098ded7d566e9e51467d994c09af29f28679486019c214477c6", "matches": false}], "matches": [], "summary": {"total_attempts": 22, "matches_found": 0, "analysis_complete": true}}