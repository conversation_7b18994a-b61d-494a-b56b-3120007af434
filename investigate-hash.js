import crypto from 'crypto';

/**
 * Investigate how the server seed is hashed
 */

const serverSeed = "58254f60f9661ee71d6c1514ec774914df925d9da9f5550524d43a12a0ed46db";
const expectedHash = "zjYhc3F9DEw/rSPAFuFICyfwOqAF0d1vQJb1dpWP/Ww=";
const clientSeed = "WeF60TQOI5ton2ddRSUnfd+sDjgk4y/co8NDd+nXYZk=";

console.log('🔍 INVESTIGATING SERVER SEED HASHING');
console.log('====================================');
console.log('Server Seed:', serverSeed);
console.log('Expected Hash:', expectedHash);
console.log('Client Seed:', clientSeed);

// Try different ways to hash the server seed
const attempts = [];

// Method 1: Direct SHA256 of hex string
const directHex = crypto.createHash('sha256').update(serverSeed, 'hex').digest('base64');
attempts.push({ method: 'SHA256(serverSeed as hex)', result: directHex });

// Method 2: SHA256 of string
const directString = crypto.createHash('sha256').update(serverSeed, 'utf8').digest('base64');
attempts.push({ method: 'SHA256(serverSeed as string)', result: directString });

// Method 3: SHA256 with client seed
const withClient = crypto.createHash('sha256').update(serverSeed + clientSeed, 'utf8').digest('base64');
attempts.push({ method: 'SHA256(serverSeed + clientSeed)', result: withClient });

// Method 4: SHA256 of server seed buffer + client seed buffer
const serverBuffer = Buffer.from(serverSeed, 'hex');
const clientBuffer = Buffer.from(clientSeed, 'base64');
const combined = Buffer.concat([serverBuffer, clientBuffer]);
const combinedHash = crypto.createHash('sha256').update(combined).digest('base64');
attempts.push({ method: 'SHA256(serverSeedBuffer + clientSeedBuffer)', result: combinedHash });

// Method 5: Try different orders
const clientFirst = crypto.createHash('sha256').update(clientSeed + serverSeed, 'utf8').digest('base64');
attempts.push({ method: 'SHA256(clientSeed + serverSeed)', result: clientFirst });

// Method 6: Try with separators
const withSeparator = crypto.createHash('sha256').update(serverSeed + ':' + clientSeed, 'utf8').digest('base64');
attempts.push({ method: 'SHA256(serverSeed + ":" + clientSeed)', result: withSeparator });

// Method 7: Try HMAC
const hmacWithClient = crypto.createHmac('sha256', clientSeed).update(serverSeed).digest('base64');
attempts.push({ method: 'HMAC-SHA256(serverSeed, clientSeed)', result: hmacWithClient });

const hmacWithServer = crypto.createHmac('sha256', serverSeed).update(clientSeed).digest('base64');
attempts.push({ method: 'HMAC-SHA256(clientSeed, serverSeed)', result: hmacWithServer });

// Method 8: Try other hash algorithms
const md5Hash = crypto.createHash('md5').update(serverSeed, 'hex').digest('base64');
attempts.push({ method: 'MD5(serverSeed)', result: md5Hash });

const sha1Hash = crypto.createHash('sha1').update(serverSeed, 'hex').digest('base64');
attempts.push({ method: 'SHA1(serverSeed)', result: sha1Hash });

const sha512Hash = crypto.createHash('sha512').update(serverSeed, 'hex').digest('base64');
attempts.push({ method: 'SHA512(serverSeed)', result: sha512Hash });

// Method 9: Try with game ID
const gameId = "93be55a8-b790-4fad-87bb-2900eb874415";
const withGameId = crypto.createHash('sha256').update(serverSeed + gameId, 'utf8').digest('base64');
attempts.push({ method: 'SHA256(serverSeed + gameId)', result: withGameId });

// Method 10: Try double hashing
const doubleHash = crypto.createHash('sha256').update(crypto.createHash('sha256').update(serverSeed, 'hex').digest('hex'), 'hex').digest('base64');
attempts.push({ method: 'SHA256(SHA256(serverSeed))', result: doubleHash });

console.log('\n🧪 HASH ATTEMPTS:');
console.log('================');

let foundMatch = false;
attempts.forEach((attempt, i) => {
  const matches = attempt.result === expectedHash;
  console.log(`${i + 1}. ${attempt.method}`);
  console.log(`   Result: ${attempt.result}`);
  console.log(`   Match: ${matches ? '✅ YES' : '❌ No'}`);
  
  if (matches) {
    foundMatch = true;
    console.log(`   🎉 FOUND THE CORRECT HASHING METHOD!`);
  }
  console.log('');
});

if (!foundMatch) {
  console.log('❌ No matching hash method found');
  console.log('\nThe server seed might be:');
  console.log('1. Hashed with a different algorithm');
  console.log('2. Combined with additional secret data');
  console.log('3. Processed through multiple steps');
  console.log('4. The API might be showing a different hash');
}

// Let's also check if the expected hash decodes to something meaningful
console.log('\n🔍 ANALYZING EXPECTED HASH:');
console.log('===========================');
const hashBuffer = Buffer.from(expectedHash, 'base64');
console.log('Hash as hex:', hashBuffer.toString('hex'));
console.log('Hash length:', hashBuffer.length, 'bytes');
console.log('Hash as binary:', hashBuffer.toString('binary'));

// Check if it could be a different encoding
console.log('\nTrying to decode as different formats:');
try {
  console.log('As UTF-8:', hashBuffer.toString('utf8'));
} catch (e) {
  console.log('As UTF-8: Invalid');
}

// Check if the server seed could be encoded differently
console.log('\n🔍 ANALYZING SERVER SEED:');
console.log('=========================');
console.log('Server seed length:', serverSeed.length, 'characters');
console.log('Server seed as buffer length:', Buffer.from(serverSeed, 'hex').length, 'bytes');

// Try interpreting server seed as different encodings
const serverAsBuffer = Buffer.from(serverSeed, 'hex');
console.log('Server seed as hex buffer:', serverAsBuffer.toString('hex'));
console.log('Server seed as base64:', serverAsBuffer.toString('base64'));

// Final verification - maybe the hash is correct but we're comparing wrong
console.log('\n🔍 FINAL VERIFICATION:');
console.log('======================');
console.log('Expected (base64):', expectedHash);
console.log('Expected (hex):', Buffer.from(expectedHash, 'base64').toString('hex'));

attempts.forEach(attempt => {
  const attemptHex = Buffer.from(attempt.result, 'base64').toString('hex');
  const expectedHex = Buffer.from(expectedHash, 'base64').toString('hex');
  
  if (attemptHex === expectedHex) {
    console.log(`✅ HEX MATCH FOUND: ${attempt.method}`);
  }
});
