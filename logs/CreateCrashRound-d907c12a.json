{"transaction_info": {"signature": "575S7J5fgnhjNJhaJ6bVeEtq2kYnzyF7XMTmULobTRYeLQjZvjWXhfELqL5Z8rdXYxgNdExokA6P5AddRVG9itML", "instruction_type": "CreateCrashRound", "custom_id": "d907c12a", "timestamp": "2025-06-24T06:36:36.692Z"}, "solana_transaction": {"blockTime": 1750746995, "meta": {"computeUnitsConsumed": 4506, "err": null, "fee": 5050, "innerInstructions": [], "logMessages": ["Program ComputeBudget111111111111111111111111111111 invoke [1]", "Program ComputeBudget111111111111111111111111111111 success", "Program ComputeBudget111111111111111111111111111111 invoke [1]", "Program ComputeBudget111111111111111111111111111111 success", "Program 6YVBrao9DSLVGk7QsXxQVPbs4XyHVtHw9oSTTXw1otEW invoke [1]", "Program log: Instruction: CreateCrashRound", "Program log: New round started with custom ID: d907c12a", "Program 6YVBrao9DSLVGk7QsXxQVPbs4XyHVtHw9oSTTXw1otEW consumed 4206 of 4657 compute units", "Program 6YVBrao9DSLVGk7QsXxQVPbs4XyHVtHw9oSTTXw1otEW success"], "postBalances": [*********, ********, 1141440, 1], "postTokenBalances": [], "preBalances": [*********, ********, 1141440, 1], "preTokenBalances": [], "rewards": [], "status": {"Ok": null}}, "slot": *********, "transaction": {"message": {"accountKeys": [{"pubkey": "EwsWeAvhBGr5vf5VWhqjy1hC2Z9guyaiLz2ErDy34rUZ", "signer": true, "source": "transaction", "writable": true}, {"pubkey": "rw7igePyZ5r7RmY1nuPU8CdorrHkrMic5sfchbwbjK8", "signer": false, "source": "transaction", "writable": true}, {"pubkey": "6YVBrao9DSLVGk7QsXxQVPbs4XyHVtHw9oSTTXw1otEW", "signer": false, "source": "transaction", "writable": false}, {"pubkey": "ComputeBudget111111111111111111111111111111", "signer": false, "source": "transaction", "writable": false}], "instructions": [{"accounts": [], "data": "GU3teK", "programId": "ComputeBudget111111111111111111111111111111", "stackHeight": null}, {"accounts": [], "data": "3GAG5eogvTjV", "programId": "ComputeBudget111111111111111111111111111111", "stackHeight": null}, {"accounts": ["rw7igePyZ5r7RmY1nuPU8CdorrHkrMic5sfchbwbjK8", "EwsWeAvhBGr5vf5VWhqjy1hC2Z9guyaiLz2ErDy34rUZ"], "data": "2A148kSFA6tqVKD9LQ6WRrpwUSxp", "programId": "6YVBrao9DSLVGk7QsXxQVPbs4XyHVtHw9oSTTXw1otEW", "stackHeight": null}], "recentBlockhash": "FqL5NzwSdHB7wcDNT2Ru62vh1w8Njz6mJkiAFCp4hKSe"}, "signatures": ["575S7J5fgnhjNJhaJ6bVeEtq2kYnzyF7XMTmULobTRYeLQjZvjWXhfELqL5Z8rdXYxgNdExokA6P5AddRVG9itML"]}, "version": "legacy"}, "api_snapshot": {"id": "d907c12a-7486-4abe-a373-70169d74fc94", "state": "accepting bets", "isCurrentStateTransactionConfirmed": false, "hashedServerSeed": "vrIw1gUwo2YgGBeG9c0LXUmpbD7eO2vRTpLpAyuQyZs=", "clientSeed": null, "crashMultiplier": null, "totalBetCount": 0, "totalBetAmount": "0", "totalBetAmountUi": "0.0000", "totalWonAmount": "0", "totalWonAmountUi": "0.0000", "startAt": null, "crashAt": null, "newGameTransactionHash": null, "startGameTransactionHash": null, "finishGameTransactionHash": null, "bets": [], "createdAt": "2025-06-24T06:36:34.595Z", "updatedAt": "2025-06-24T06:36:34.595Z"}, "correlation": {"api_game_id": "d907c12a-7486-4abe-a373-70169d74fc94", "api_state": "accepting bets", "hashed_server_seed": "vrIw1gUwo2YgGBeG9c0LXUmpbD7eO2vRTpLpAyuQyZs=", "client_seed": null, "crash_multiplier": null, "transaction_matches_api": false}}