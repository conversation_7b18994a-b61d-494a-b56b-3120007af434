import fs from 'fs';
import crypto from 'crypto';

/**
 * Comprehensive analysis of complete game data to reverse engineer server seed
 */

// Load the complete game data
const gameData = JSON.parse(fs.readFileSync('./complete-games/complete-game-93be55a8.json', 'utf8'));

console.log('🎯 COMPLETE GAME ANALYSIS');
console.log('========================');
console.log('Game ID:', gameData.game_info.custom_id);
console.log('API Game ID:', gameData.game_info.api_game_id);

// Extract the key data we need to analyze
const serverSeed = gameData.api_snapshots.EndCrashRound.serverSeed;
const hashedServerSeed = gameData.api_snapshots.EndCrashRound.hashedServerSeed;
const clientSeed = gameData.api_snapshots.EndCrashRound.clientSeed;
const crashMultiplier = gameData.api_snapshots.EndCrashRound.crashMultiplier;

console.log('\n🔑 KEY DATA:');
console.log('Server Seed:', serverSeed);
console.log('Hashed Server Seed:', hashedServerSeed);
console.log('Client Seed:', clientSeed);
console.log('Crash Multiplier:', crashMultiplier);

// Verify the server seed hash
const computedHash = crypto.createHash('sha256').update(serverSeed, 'hex').digest('base64');
console.log('\n✅ VERIFICATION:');
console.log('Computed hash of server seed:', computedHash);
console.log('Expected hashed server seed:', hashedServerSeed);
console.log('Hash verification:', computedHash === hashedServerSeed ? '✅ MATCH' : '❌ NO MATCH');

// Extract transaction data for each stage
const stages = ['CreateCrashRound', 'StartCrashRound', 'EndCrashRound'];
const transactionData = {};

stages.forEach(stage => {
  const stageData = gameData.stages[stage];
  const instruction = stageData.transaction.transaction.message.instructions.find(
    inst => inst.programId === '6YVBrao9DSLVGk7QsXxQVPbs4XyHVtHw9oSTTXw1otEW'
  );
  
  if (instruction) {
    const dataBuffer = Buffer.from(instruction.data, 'base64');
    transactionData[stage] = {
      base64: instruction.data,
      hex: dataBuffer.toString('hex'),
      bytes: Array.from(dataBuffer),
      length: dataBuffer.length,
      signature: stageData.signature,
      blockTime: stageData.transaction.blockTime,
      slot: stageData.transaction.slot
    };
  }
});

console.log('\n📊 TRANSACTION DATA:');
stages.forEach(stage => {
  const data = transactionData[stage];
  if (data) {
    console.log(`${stage}:`);
    console.log(`  Data (hex): ${data.hex}`);
    console.log(`  Length: ${data.length} bytes`);
    console.log(`  Signature: ${data.signature}`);
    console.log(`  Block Time: ${data.blockTime}`);
    console.log(`  Slot: ${data.slot}`);
  }
});

// Now try to find correlations with the server seed
console.log('\n🔍 SERVER SEED CORRELATION ANALYSIS');
console.log('===================================');

const serverSeedBuffer = Buffer.from(serverSeed, 'hex');
const attempts = [];

// Method 1: Direct correlation with individual stage data
stages.forEach(stage => {
  const data = transactionData[stage];
  if (data) {
    const dataBuffer = Buffer.from(data.hex, 'hex');
    
    // Try direct hash
    const directHash = crypto.createHash('sha256').update(dataBuffer).digest('hex');
    attempts.push({
      method: `SHA256(${stage}_data)`,
      input: data.hex,
      output: directHash,
      matches: directHash === serverSeed
    });
    
    // Try with stage name prefix
    const withStage = Buffer.concat([Buffer.from(stage), dataBuffer]);
    const stageHash = crypto.createHash('sha256').update(withStage).digest('hex');
    attempts.push({
      method: `SHA256("${stage}" + ${stage}_data)`,
      input: stage + data.hex,
      output: stageHash,
      matches: stageHash === serverSeed
    });
    
    // Try with signature
    const sigBuffer = Buffer.from(data.signature, 'base64');
    const withSig = Buffer.concat([dataBuffer, sigBuffer]);
    const sigHash = crypto.createHash('sha256').update(withSig).digest('hex');
    attempts.push({
      method: `SHA256(${stage}_data + signature)`,
      input: data.hex + data.signature,
      output: sigHash,
      matches: sigHash === serverSeed
    });
    
    // Try with block time
    const timeBuffer = Buffer.alloc(8);
    timeBuffer.writeBigUInt64LE(BigInt(data.blockTime));
    const withTime = Buffer.concat([dataBuffer, timeBuffer]);
    const timeHash = crypto.createHash('sha256').update(withTime).digest('hex');
    attempts.push({
      method: `SHA256(${stage}_data + blockTime)`,
      input: data.hex + timeBuffer.toString('hex'),
      output: timeHash,
      matches: timeHash === serverSeed
    });
    
    // Try with slot
    const slotBuffer = Buffer.alloc(8);
    slotBuffer.writeBigUInt64LE(BigInt(data.slot));
    const withSlot = Buffer.concat([dataBuffer, slotBuffer]);
    const slotHash = crypto.createHash('sha256').update(withSlot).digest('hex');
    attempts.push({
      method: `SHA256(${stage}_data + slot)`,
      input: data.hex + slotBuffer.toString('hex'),
      output: slotHash,
      matches: slotHash === serverSeed
    });
  }
});

// Method 2: Combined stage data
const allStageData = stages.map(stage => transactionData[stage]?.hex).filter(Boolean);
if (allStageData.length >= 2) {
  const combined = allStageData.join('');
  const combinedBuffer = Buffer.from(combined, 'hex');
  const combinedHash = crypto.createHash('sha256').update(combinedBuffer).digest('hex');
  attempts.push({
    method: 'SHA256(all_stages_combined)',
    input: combined,
    output: combinedHash,
    matches: combinedHash === serverSeed
  });
}

// Method 3: Try with game ID
const gameId = gameData.game_info.api_game_id;
const gameIdBuffer = Buffer.from(gameId.replace(/-/g, ''), 'hex');
stages.forEach(stage => {
  const data = transactionData[stage];
  if (data) {
    const dataBuffer = Buffer.from(data.hex, 'hex');
    const withGameId = Buffer.concat([dataBuffer, gameIdBuffer]);
    const gameIdHash = crypto.createHash('sha256').update(withGameId).digest('hex');
    attempts.push({
      method: `SHA256(${stage}_data + gameId)`,
      input: data.hex + gameId.replace(/-/g, ''),
      output: gameIdHash,
      matches: gameIdHash === serverSeed
    });
  }
});

// Method 4: Try with custom ID
const customId = gameData.game_info.custom_id;
const customIdBuffer = Buffer.from(customId, 'hex');
stages.forEach(stage => {
  const data = transactionData[stage];
  if (data) {
    const dataBuffer = Buffer.from(data.hex, 'hex');
    const withCustomId = Buffer.concat([dataBuffer, customIdBuffer]);
    const customIdHash = crypto.createHash('sha256').update(withCustomId).digest('hex');
    attempts.push({
      method: `SHA256(${stage}_data + customId)`,
      input: data.hex + customId,
      output: customIdHash,
      matches: customIdHash === serverSeed
    });
  }
});

// Check for matches
const matches = attempts.filter(attempt => attempt.matches);

console.log('\n🎯 RESULTS:');
if (matches.length > 0) {
  console.log(`🎉 FOUND ${matches.length} MATCH(ES)!`);
  matches.forEach((match, i) => {
    console.log(`\n✅ Match ${i + 1}:`);
    console.log(`Method: ${match.method}`);
    console.log(`Input: ${match.input.substring(0, 100)}${match.input.length > 100 ? '...' : ''}`);
    console.log(`Output: ${match.output}`);
    console.log(`Expected: ${serverSeed}`);
  });
} else {
  console.log('❌ No direct matches found');
  console.log('\nTop 5 closest attempts:');
  attempts.slice(0, 5).forEach((attempt, i) => {
    console.log(`\n${i + 1}. ${attempt.method}`);
    console.log(`   Output: ${attempt.output.substring(0, 32)}...`);
    console.log(`   Expected: ${serverSeed.substring(0, 32)}...`);
  });
}

// Save detailed analysis
const analysisResult = {
  game_info: gameData.game_info,
  key_data: {
    serverSeed,
    hashedServerSeed,
    clientSeed,
    crashMultiplier
  },
  transaction_data: transactionData,
  correlation_attempts: attempts,
  matches: matches,
  summary: {
    total_attempts: attempts.length,
    matches_found: matches.length,
    analysis_complete: true
  }
};

fs.writeFileSync('./server-seed-analysis.json', JSON.stringify(analysisResult, null, 2));
console.log('\n💾 Detailed analysis saved to server-seed-analysis.json');

console.log('\n📋 SUMMARY:');
console.log(`Total correlation attempts: ${attempts.length}`);
console.log(`Matches found: ${matches.length}`);
console.log(`Server seed successfully extracted: ✅`);
console.log(`Hash verification: ${computedHash === hashedServerSeed ? '✅' : '❌'}`);
