import fs from 'fs';
import crypto from 'crypto';

/**
 * Comprehensive UUID combinations and chained hashing analysis
 */

// Load all complete game files
const gameFiles = [
  'complete-game-93be55a8.json',
  'complete-game-acfd826c.json', 
  'complete-game-ad84e15d.json'
];

const games = gameFiles.map(file => {
  try {
    return JSON.parse(fs.readFileSync(`./complete-games/${file}`, 'utf8'));
  } catch (e) {
    console.log(`Could not load ${file}`);
    return null;
  }
}).filter(Boolean);

console.log('🎯 COMPREHENSIVE UUID COMBINATIONS & CHAINED HASHING ANALYSIS');
console.log('=============================================================');
console.log(`Analyzing ${games.length} complete games`);

// Extract game data
const gameData = games.map(game => ({
  customId: game.game_info.custom_id,
  fullGameId: game.game_info.api_game_id,
  serverSeed: game.api_snapshots.EndCrashRound?.serverSeed,
  clientSeed: game.api_snapshots.EndCrashRound?.clientSeed,
  crashMultiplier: game.api_snapshots.EndCrashRound?.crashMultiplier
}));

// Function to generate all combinations of array elements
function generateCombinations(arr) {
  const combinations = [];
  
  // Single elements
  arr.forEach(item => combinations.push([item]));
  
  // Pairs
  for (let i = 0; i < arr.length; i++) {
    for (let j = i + 1; j < arr.length; j++) {
      combinations.push([arr[i], arr[j]]);
      combinations.push([arr[j], arr[i]]); // Reverse order
    }
  }
  
  // Triplets
  for (let i = 0; i < arr.length; i++) {
    for (let j = i + 1; j < arr.length; j++) {
      for (let k = j + 1; k < arr.length; k++) {
        combinations.push([arr[i], arr[j], arr[k]]);
        combinations.push([arr[k], arr[j], arr[i]]); // Reverse order
        combinations.push([arr[j], arr[i], arr[k]]); // Mixed order
        combinations.push([arr[j], arr[k], arr[i]]); // Mixed order
        combinations.push([arr[k], arr[i], arr[j]]); // Mixed order
        combinations.push([arr[i], arr[k], arr[j]]); // Mixed order
      }
    }
  }
  
  // Quadruplets
  for (let i = 0; i < arr.length; i++) {
    for (let j = i + 1; j < arr.length; j++) {
      for (let k = j + 1; k < arr.length; k++) {
        for (let l = k + 1; l < arr.length; l++) {
          combinations.push([arr[i], arr[j], arr[k], arr[l]]);
          combinations.push([arr[l], arr[k], arr[j], arr[i]]); // Reverse
          // Add a few more permutations
          combinations.push([arr[j], arr[i], arr[l], arr[k]]);
          combinations.push([arr[k], arr[l], arr[i], arr[j]]);
        }
      }
    }
  }
  
  // All 5 elements in different orders
  combinations.push([...arr]); // Original order
  combinations.push([...arr].reverse()); // Reverse order
  
  return combinations;
}

// Function to try chained hashing
function tryChainedHashing(input, algorithms) {
  let result = input;
  
  for (const algo of algorithms) {
    if (algo === 'sha256') {
      result = crypto.createHash('sha256').update(result, 'utf8').digest('hex');
    } else if (algo === 'md5') {
      result = crypto.createHash('md5').update(result, 'utf8').digest('hex');
    } else if (algo === 'sha1') {
      result = crypto.createHash('sha1').update(result, 'utf8').digest('hex');
    } else if (algo === 'sha512') {
      result = crypto.createHash('sha512').update(result, 'utf8').digest('hex');
    }
  }
  
  return result;
}

function analyzeGame(game) {
  console.log(`\n🎯 GAME ${game.customId}:`);
  console.log(`Full Game ID: ${game.fullGameId}`);
  console.log(`Target Server Seed: ${game.serverSeed}`);
  console.log('='.repeat(70));
  
  // Split UUID into parts
  const parts = game.fullGameId.split('-');
  const [part1, part2, part3, part4, part5] = parts;
  
  console.log(`UUID Parts: ${part1} | ${part2} | ${part3} | ${part4} | ${part5}`);
  
  const matches = [];
  let totalAttempts = 0;
  
  // Generate all combinations
  const combinations = generateCombinations(parts);
  console.log(`\nGenerated ${combinations.length} combinations to test`);
  
  // Hash algorithms to chain
  const chainedAlgorithms = [
    ['sha256'],
    ['md5'],
    ['sha1'],
    ['md5', 'sha256'],
    ['sha1', 'sha256'],
    ['sha256', 'md5'],
    ['sha256', 'sha1'],
    ['md5', 'sha1', 'sha256'],
    ['sha1', 'md5', 'sha256'],
    ['sha256', 'md5', 'sha1'],
    ['sha512'],
    ['sha512', 'sha256'],
    ['md5', 'sha512', 'sha256']
  ];
  
  console.log('\n🔍 Testing combinations with chained hashing...');
  
  // Test each combination with each chaining algorithm
  combinations.forEach((combo, comboIndex) => {
    const comboString = combo.join('');
    const comboStringWithDashes = combo.join('-');
    
    chainedAlgorithms.forEach((algos, algoIndex) => {
      // Test without dashes
      const result1 = tryChainedHashing(comboString, algos);
      totalAttempts++;
      
      if (result1 === game.serverSeed) {
        const match = {
          combination: combo,
          input: comboString,
          algorithms: algos,
          result: result1,
          description: `${algos.join('→')}(${combo.join('')})`
        };
        matches.push(match);
        console.log(`🎉 BREAKTHROUGH! ${match.description} = server seed!`);
        console.log(`   Input: ${match.input}`);
        console.log(`   Result: ${match.result}`);
      }
      
      // Test with dashes
      const result2 = tryChainedHashing(comboStringWithDashes, algos);
      totalAttempts++;
      
      if (result2 === game.serverSeed) {
        const match = {
          combination: combo,
          input: comboStringWithDashes,
          algorithms: algos,
          result: result2,
          description: `${algos.join('→')}(${combo.join('-')})`
        };
        matches.push(match);
        console.log(`🎉 BREAKTHROUGH! ${match.description} = server seed!`);
        console.log(`   Input: ${match.input}`);
        console.log(`   Result: ${match.result}`);
      }
    });
    
    // Progress indicator for large combinations
    if ((comboIndex + 1) % 50 === 0) {
      console.log(`   Tested ${comboIndex + 1}/${combinations.length} combinations...`);
    }
  });
  
  // Additional rotations for promising combinations
  console.log('\n🔄 Testing rotations of individual parts...');
  
  parts.forEach((part, partIndex) => {
    // Try rotating each part
    for (let i = 1; i < part.length; i++) {
      const rotated = part.substring(i) + part.substring(0, i);
      
      chainedAlgorithms.forEach(algos => {
        const result = tryChainedHashing(rotated, algos);
        totalAttempts++;
        
        if (result === game.serverSeed) {
          const match = {
            combination: [rotated],
            input: rotated,
            algorithms: algos,
            result: result,
            description: `${algos.join('→')}(${part} rotated ${i})`
          };
          matches.push(match);
          console.log(`🎉 BREAKTHROUGH! ${match.description} = server seed!`);
        }
      });
    }
  });
  
  // Test with custom ID mixed in
  console.log('\n🔀 Testing with custom ID combinations...');
  
  const customIdCombos = [
    [game.customId, part1],
    [part1, game.customId],
    [game.customId, part5],
    [part5, game.customId],
    [game.customId, part2, part3],
    [part2, game.customId, part4]
  ];
  
  customIdCombos.forEach(combo => {
    const comboString = combo.join('');
    
    chainedAlgorithms.forEach(algos => {
      const result = tryChainedHashing(comboString, algos);
      totalAttempts++;
      
      if (result === game.serverSeed) {
        const match = {
          combination: combo,
          input: comboString,
          algorithms: algos,
          result: result,
          description: `${algos.join('→')}(${combo.join('+')})`
        };
        matches.push(match);
        console.log(`🎉 BREAKTHROUGH! ${match.description} = server seed!`);
      }
    });
  });
  
  console.log(`\n📊 Tested ${totalAttempts} total combinations for this game`);
  
  if (matches.length === 0) {
    console.log('❌ No matches found for this game');
    
    // Check for partial matches (first 16 chars)
    console.log('\n🔍 Checking for partial matches...');
    let partialMatches = 0;
    
    combinations.slice(0, 20).forEach(combo => { // Check first 20 combinations for partials
      const comboString = combo.join('');
      chainedAlgorithms.slice(0, 5).forEach(algos => { // Check first 5 algorithms
        const result = tryChainedHashing(comboString, algos);
        if (result.substring(0, 16) === game.serverSeed.substring(0, 16)) {
          partialMatches++;
          console.log(`   Partial: ${algos.join('→')}(${combo.join('')}) = ${result.substring(0, 16)}...`);
        }
      });
    });
    
    if (partialMatches === 0) {
      console.log('   No partial matches found either');
    }
  }
  
  return { matches, totalAttempts };
}

// Analyze each game
const allResults = [];
gameData.forEach(game => {
  const result = analyzeGame(game);
  allResults.push({
    gameId: game.customId,
    matches: result.matches,
    totalAttempts: result.totalAttempts
  });
});

console.log('\n📊 FINAL RESULTS SUMMARY:');
console.log('=========================');

const totalMatches = allResults.reduce((sum, result) => sum + result.matches.length, 0);
const totalAttempts = allResults.reduce((sum, result) => sum + result.totalAttempts, 0);

console.log(`Total attempts across all games: ${totalAttempts}`);
console.log(`Total matches found: ${totalMatches}`);

if (totalMatches > 0) {
  console.log('\n🎉 MATCHES FOUND:');
  allResults.forEach(result => {
    if (result.matches.length > 0) {
      console.log(`\nGame ${result.gameId}:`);
      result.matches.forEach(match => {
        console.log(`  ✅ ${match.description}`);
        console.log(`     Input: ${match.input}`);
        console.log(`     Result: ${match.result}`);
      });
    }
  });
} else {
  console.log('\n❌ No exact matches found across all games');
  console.log('\nThis comprehensive analysis tested:');
  console.log('✅ All possible UUID part combinations');
  console.log('✅ Multiple permutation orders');
  console.log('✅ Chained hashing (MD5→SHA256, etc.)');
  console.log('✅ Rotations of individual parts');
  console.log('✅ Custom ID combinations');
  console.log('✅ With and without dashes');
}

// Save results
const analysisResults = {
  summary: {
    total_games: gameData.length,
    total_attempts: totalAttempts,
    total_matches: totalMatches,
    analysis_timestamp: new Date().toISOString()
  },
  results: allResults
};

fs.writeFileSync('./uuid-combinations-analysis.json', JSON.stringify(analysisResults, null, 2));
console.log('\n💾 Analysis saved to uuid-combinations-analysis.json');
