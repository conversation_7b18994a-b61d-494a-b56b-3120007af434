import crypto from 'crypto';

/**
 * Focus on correlating CreateCrashRound data with server seed
 */

const serverSeed = "58254f60f9661ee71d6c1514ec774914df925d9da9f5550524d43a12a0ed46db";
const createData = "6a54a0fd2c6bbc90d04d4d9d97"; // 13 bytes from CreateCrashRound
const gameId = "93be55a8";
const fullGameId = "93be55a8-b790-4fad-87bb-2900eb874415";

console.log('🎯 CORRELATING CreateCrashRound DATA WITH SERVER SEED');
console.log('===================================================');
console.log('Server Seed:', serverSeed);
console.log('CreateCrashRound Data:', createData);
console.log('Game ID:', gameId);
console.log('Full Game ID:', fullGameId);

const attempts = [];

// Method 1: Direct hash of the 13 bytes
const dataBuffer = Buffer.from(createData, 'hex');
const directHash = crypto.createHash('sha256').update(dataBuffer).digest('hex');
attempts.push({
  method: 'SHA256(createData)',
  input: createData,
  output: directHash,
  matches: directHash === serverSeed
});

// Method 2: Hash as string
const stringHash = crypto.createHash('sha256').update(createData, 'utf8').digest('hex');
attempts.push({
  method: 'SHA256(createData as string)',
  input: createData,
  output: stringHash,
  matches: stringHash === serverSeed
});

// Method 3: Try different parts of the data
for (let start = 0; start < createData.length - 7; start += 2) {
  for (let len = 8; len <= createData.length - start; len += 2) {
    const substring = createData.substr(start, len);
    const subHash = crypto.createHash('sha256').update(substring, 'utf8').digest('hex');
    attempts.push({
      method: `SHA256(bytes_${start/2}_to_${(start+len)/2-1})`,
      input: substring,
      output: subHash,
      matches: subHash === serverSeed
    });
  }
}

// Method 4: Try with game ID combinations
const withGameId = crypto.createHash('sha256').update(createData + gameId, 'utf8').digest('hex');
attempts.push({
  method: 'SHA256(createData + gameId)',
  input: createData + gameId,
  output: withGameId,
  matches: withGameId === serverSeed
});

const withFullGameId = crypto.createHash('sha256').update(createData + fullGameId, 'utf8').digest('hex');
attempts.push({
  method: 'SHA256(createData + fullGameId)',
  input: createData + fullGameId,
  output: withFullGameId,
  matches: withFullGameId === serverSeed
});

// Method 5: Try reversing the data
const reversedData = createData.split('').reverse().join('');
const reversedHash = crypto.createHash('sha256').update(reversedData, 'utf8').digest('hex');
attempts.push({
  method: 'SHA256(reversed_createData)',
  input: reversedData,
  output: reversedHash,
  matches: reversedHash === serverSeed
});

// Method 6: Try byte-wise reversal
const reversedBytes = Buffer.from(createData, 'hex').reverse().toString('hex');
const reversedBytesHash = crypto.createHash('sha256').update(reversedBytes, 'utf8').digest('hex');
attempts.push({
  method: 'SHA256(byte_reversed_createData)',
  input: reversedBytes,
  output: reversedBytesHash,
  matches: reversedBytesHash === serverSeed
});

// Method 7: Try XOR with known values
const gameIdBuffer = Buffer.from(gameId, 'hex');
if (dataBuffer.length >= gameIdBuffer.length) {
  const xorResult = Buffer.alloc(gameIdBuffer.length);
  for (let i = 0; i < gameIdBuffer.length; i++) {
    xorResult[i] = dataBuffer[i] ^ gameIdBuffer[i];
  }
  const xorHash = crypto.createHash('sha256').update(xorResult).digest('hex');
  attempts.push({
    method: 'SHA256(createData XOR gameId)',
    input: xorResult.toString('hex'),
    output: xorHash,
    matches: xorHash === serverSeed
  });
}

// Method 8: Try interpreting as different data types and hashing
const u32_1 = dataBuffer.readUInt32LE(0);
const u32_2 = dataBuffer.readUInt32LE(4);
const u32_3 = dataBuffer.readUInt32LE(8);

const u32Hash1 = crypto.createHash('sha256').update(u32_1.toString()).digest('hex');
attempts.push({
  method: 'SHA256(first_u32_as_string)',
  input: u32_1.toString(),
  output: u32Hash1,
  matches: u32Hash1 === serverSeed
});

const combinedU32 = u32_1.toString() + u32_2.toString() + u32_3.toString();
const combinedU32Hash = crypto.createHash('sha256').update(combinedU32).digest('hex');
attempts.push({
  method: 'SHA256(all_u32_concatenated)',
  input: combinedU32,
  output: combinedU32Hash,
  matches: combinedU32Hash === serverSeed
});

// Method 9: Try with different hash algorithms
const md5Hash = crypto.createHash('md5').update(dataBuffer).digest('hex');
const sha1Hash = crypto.createHash('sha1').update(dataBuffer).digest('hex');
const sha512Hash = crypto.createHash('sha512').update(dataBuffer).digest('hex');

attempts.push({
  method: 'MD5(createData)',
  input: createData,
  output: md5Hash,
  matches: md5Hash === serverSeed
});

attempts.push({
  method: 'SHA1(createData)',
  input: createData,
  output: sha1Hash,
  matches: sha1Hash === serverSeed
});

attempts.push({
  method: 'SHA512(createData)',
  input: createData,
  output: sha512Hash.substring(0, 64), // Truncate to same length as server seed
  matches: sha512Hash.substring(0, 64) === serverSeed
});

// Method 10: Try HMAC with different keys
const hmacGameId = crypto.createHmac('sha256', gameId).update(dataBuffer).digest('hex');
attempts.push({
  method: 'HMAC-SHA256(createData, gameId)',
  input: createData,
  output: hmacGameId,
  matches: hmacGameId === serverSeed
});

// Method 11: Check if createData IS the server seed (or part of it)
console.log('\n🔍 DIRECT COMPARISON:');
console.log('CreateData:', createData);
console.log('Server Seed:', serverSeed);
console.log('CreateData in ServerSeed?', serverSeed.includes(createData));
console.log('ServerSeed in CreateData?', createData.includes(serverSeed.substring(0, createData.length)));

// Check for any overlapping substrings
for (let len = 8; len <= Math.min(createData.length, serverSeed.length); len += 2) {
  for (let start = 0; start <= createData.length - len; start += 2) {
    const substring = createData.substr(start, len);
    if (serverSeed.includes(substring)) {
      console.log(`✅ Found overlap: "${substring}" at position ${serverSeed.indexOf(substring)} in server seed`);
    }
  }
}

// Method 12: Try interpreting as a seed for a PRNG
console.log('\n🎲 PRNG ANALYSIS:');
const seedAsNumber = parseInt(createData, 16);
console.log('CreateData as number:', seedAsNumber);

// Simple linear congruential generator test
function lcg(seed, a = 1664525, c = 1013904223, m = Math.pow(2, 32)) {
  return ((a * seed + c) % m);
}

const lcgResult = lcg(seedAsNumber);
const lcgHash = crypto.createHash('sha256').update(lcgResult.toString()).digest('hex');
attempts.push({
  method: 'SHA256(LCG(createData))',
  input: lcgResult.toString(),
  output: lcgHash,
  matches: lcgHash === serverSeed
});

// Check results
console.log('\n🎯 CORRELATION RESULTS:');
console.log('======================');

const matches = attempts.filter(attempt => attempt.matches);

if (matches.length > 0) {
  console.log(`🎉 FOUND ${matches.length} MATCH(ES)!`);
  matches.forEach((match, i) => {
    console.log(`\n✅ Match ${i + 1}:`);
    console.log(`Method: ${match.method}`);
    console.log(`Input: ${match.input}`);
    console.log(`Output: ${match.output}`);
  });
} else {
  console.log('❌ No direct correlations found');
  
  console.log('\nClosest attempts (first 10):');
  attempts.slice(0, 10).forEach((attempt, i) => {
    console.log(`${i + 1}. ${attempt.method}`);
    console.log(`   Input: ${attempt.input.substring(0, 50)}${attempt.input.length > 50 ? '...' : ''}`);
    console.log(`   Output: ${attempt.output.substring(0, 32)}...`);
    console.log(`   Expected: ${serverSeed.substring(0, 32)}...`);
    console.log('');
  });
}

console.log(`\n📊 SUMMARY:`);
console.log(`Total attempts: ${attempts.length}`);
console.log(`Matches found: ${matches.length}`);

if (matches.length === 0) {
  console.log('\n💡 POSSIBLE EXPLANATIONS:');
  console.log('1. Server seed is generated independently (not from transaction data)');
  console.log('2. Additional server-side entropy is mixed in');
  console.log('3. More complex cryptographic operations are used');
  console.log('4. The data represents something else (game parameters, not seed material)');
  console.log('5. Multiple transactions or external data sources are combined');
}
