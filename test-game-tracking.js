import fs from 'fs';
import { decodeInstructionData } from './reverse-engineer.js';

/**
 * Test the game tracking system with mock data
 */
function testGameTracking() {
  console.log('🧪 Testing Game Tracking System');
  
  // Mock game tracking state
  let currentGameData = {
    gameId: null,
    customId: null,
    stages: {
      CreateCrashRound: null,
      StartCrashRound: null,
      EndCrashRound: null
    },
    apiData: null,
    startTime: null
  };
  
  // Mock API data
  const mockApiData = {
    id: "test-game-123",
    state: "accepting bets",
    hashedServerSeed: "iF+JU5v9a9DEDH/bodn0/XbQG9V9wiOgprLn/ppeMNM=",
    clientSeed: null,
    newGameTransactionHash: "mock-create-tx-hash"
  };
  
  // Mock transaction data for each stage
  const mockTransactions = {
    CreateCrashRound: {
      transaction: {
        message: {
          instructions: [{
            programId: "6YVBrao9DSLVGk7QsXxQVPbs4XyHVtHw9oSTTXw1otEW",
            data: "2A148kSFA6tqVKD9LQTWYTXq2SbP", // From our example
            accounts: ["account1", "account2"]
          }]
        },
        signatures: ["mock-create-tx-hash"]
      },
      meta: {
        logMessages: [
          "Program log: Instruction: CreateCrashRound",
          "Program log: New round started with custom ID: testgame1"
        ]
      }
    },
    StartCrashRound: {
      transaction: {
        message: {
          instructions: [{
            programId: "6YVBrao9DSLVGk7QsXxQVPbs4XyHVtHw9oSTTXw1otEW",
            data: "3B259lTGB7urWLE8MRUWYUXr3TcQ", // Mock data
            accounts: ["account1", "account2"]
          }]
        },
        signatures: ["mock-start-tx-hash"]
      },
      meta: {
        logMessages: [
          "Program log: Instruction: StartCrashRound",
          "Program log: Game started with custom ID: testgame1"
        ]
      }
    },
    EndCrashRound: {
      transaction: {
        message: {
          instructions: [{
            programId: "6YVBrao9DSLVGk7QsXxQVPbs4XyHVtHw9oSTTXw1otEW",
            data: "4C369mUHC8vsXMF9NSVXZVYs4UdR", // Mock data
            accounts: ["account1", "account2"]
          }]
        },
        signatures: ["mock-end-tx-hash"]
      },
      meta: {
        logMessages: [
          "Program log: Instruction: EndCrashRound",
          "Program log: Game ended with custom ID: testgame1, multiplier: 2.45"
        ]
      }
    }
  };
  
  // Simulate the game tracking process
  console.log('\n--- Simulating CreateCrashRound ---');
  currentGameData = {
    gameId: mockApiData.id,
    customId: "testgame1",
    stages: {
      CreateCrashRound: null,
      StartCrashRound: null,
      EndCrashRound: null
    },
    apiData: mockApiData,
    startTime: new Date().toISOString()
  };
  
  // Add each stage
  for (const [stageName, transactionData] of Object.entries(mockTransactions)) {
    console.log(`\n--- Adding ${stageName} ---`);
    
    // Decode the instruction
    const targetInstructions = transactionData.transaction.message.instructions.filter(
      instruction => instruction.programId === "6YVBrao9DSLVGk7QsXxQVPbs4XyHVtHw9oSTTXw1otEW"
    );
    
    const decodedInstructions = targetInstructions.map(instruction => ({
      raw_data: instruction.data,
      decoded_data: decodeInstructionData(instruction.data),
      accounts: instruction.accounts
    }));
    
    currentGameData.stages[stageName] = {
      transaction: transactionData,
      signature: transactionData.transaction.signatures[0],
      timestamp: new Date().toISOString(),
      custom_id: "testgame1",
      decoded_instructions: decodedInstructions
    };
    
    console.log(`✅ Added ${stageName}`);
    console.log(`Decoded data:`, decodedInstructions[0]?.decoded_data?.instruction_name || 'Unknown');
  }
  
  // Check if complete
  const isComplete = Object.values(currentGameData.stages).every(stage => stage !== null);
  console.log(`\n🎯 Game complete: ${isComplete}`);
  
  if (isComplete) {
    // Save the complete game data
    const completeGameData = {
      game_info: {
        custom_id: currentGameData.customId,
        api_game_id: currentGameData.gameId,
        start_time: currentGameData.startTime,
        completion_time: new Date().toISOString(),
        is_complete: true
      },
      api_data: currentGameData.apiData,
      stages: currentGameData.stages,
      analysis: {
        note: "Test complete game data for reverse engineering server seed",
        stages_captured: Object.keys(currentGameData.stages),
        total_stages: 3
      }
    };
    
    const filepath = `./complete-games/test-complete-game-${currentGameData.customId}.json`;
    fs.writeFileSync(filepath, JSON.stringify(completeGameData, null, 2));
    console.log(`💾 Test game data saved to: ${filepath}`);
    
    // Perform basic analysis
    console.log('\n🔬 BASIC ANALYSIS');
    console.log('Hashed Server Seed:', mockApiData.hashedServerSeed);
    
    for (const [stageName, stageData] of Object.entries(currentGameData.stages)) {
      if (stageData.decoded_instructions.length > 0) {
        const decoded = stageData.decoded_instructions[0].decoded_data;
        console.log(`${stageName} data:`, decoded.data_hex || 'No data');
      }
    }
  }
}

// Run the test
testGameTracking();
