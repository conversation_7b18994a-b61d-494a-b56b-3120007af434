import fs from 'fs';
import { Buffer } from 'buffer';

/**
 * Decode instruction data from all 3 stages of the complete game
 */

// Load the complete game data
const gameData = JSON.parse(fs.readFileSync('./complete-games/complete-game-93be55a8.json', 'utf8'));

console.log('🔍 DECODING INSTRUCTION DATA FOR ALL STAGES');
console.log('===========================================');
console.log('Game ID:', gameData.game_info.custom_id);
console.log('Server Seed:', gameData.api_snapshots.EndCrashRound.serverSeed);

const stages = ['CreateCrashRound', 'StartCrashRound', 'EndCrashRound'];
const decodedData = {};

stages.forEach(stage => {
  console.log(`\n📊 ${stage.toUpperCase()}`);
  console.log('='.repeat(stage.length + 4));
  
  const stageData = gameData.stages[stage];
  const instruction = stageData.transaction.transaction.message.instructions.find(
    inst => inst.programId === '6YVBrao9DSLVGk7QsXxQVPbs4XyHVtHw9oSTTXw1otEW'
  );
  
  if (instruction) {
    const base64Data = instruction.data;
    const buffer = Buffer.from(base64Data, 'base64');
    const hexData = buffer.toString('hex');
    
    console.log('Base64 Data:', base64Data);
    console.log('Hex Data:', hexData);
    console.log('Length:', buffer.length, 'bytes');
    console.log('Raw Bytes:', Array.from(buffer));
    
    // Analyze the structure
    const analysis = {
      base64: base64Data,
      hex: hexData,
      length: buffer.length,
      bytes: Array.from(buffer),
      interpretations: []
    };
    
    // Check if this looks like an Anchor discriminator (first 8 bytes)
    if (buffer.length >= 8) {
      const discriminator = buffer.slice(0, 8);
      const data = buffer.slice(8);
      
      console.log('\n🔍 Anchor Program Analysis:');
      console.log('Discriminator (8 bytes):', discriminator.toString('hex'));
      console.log('Data after discriminator:', data.toString('hex'));
      console.log('Data length:', data.length, 'bytes');
      
      analysis.anchor = {
        discriminator: discriminator.toString('hex'),
        data: data.toString('hex'),
        data_length: data.length
      };
      
      // Try to interpret the data section
      if (data.length > 0) {
        console.log('\n📋 Data Section Interpretations:');
        
        // As individual bytes
        console.log('As bytes:', Array.from(data));
        analysis.interpretations.push({
          type: 'bytes',
          value: Array.from(data)
        });
        
        // As little-endian integers
        if (data.length >= 4) {
          const u32_le = data.readUInt32LE(0);
          console.log('First 4 bytes as u32 LE:', u32_le, `(0x${u32_le.toString(16)})`);
          analysis.interpretations.push({
            type: 'u32_le',
            value: u32_le,
            hex: u32_le.toString(16)
          });
        }
        
        if (data.length >= 8) {
          const u64_le = data.readBigUInt64LE(0);
          console.log('First 8 bytes as u64 LE:', u64_le.toString(), `(0x${u64_le.toString(16)})`);
          analysis.interpretations.push({
            type: 'u64_le',
            value: u64_le.toString(),
            hex: u64_le.toString(16)
          });
        }
        
        // Try to find patterns - check if any bytes match parts of server seed or game ID
        const serverSeed = gameData.api_snapshots.EndCrashRound.serverSeed;
        const customId = gameData.game_info.custom_id;
        
        console.log('\n🔍 Pattern Analysis:');
        
        // Check if any 4-byte sequences match parts of server seed
        for (let i = 0; i <= data.length - 4; i++) {
          const fourBytes = data.slice(i, i + 4);
          const fourBytesHex = fourBytes.toString('hex');
          const fourBytesLE = fourBytes.readUInt32LE(0);
          const fourBytesBE = fourBytes.readUInt32BE(0);
          
          console.log(`Bytes ${i}-${i+3}: ${fourBytesHex} (LE: ${fourBytesLE}, BE: ${fourBytesBE})`);
          
          // Check if this appears in server seed
          if (serverSeed.includes(fourBytesHex)) {
            console.log(`  ✅ Found in server seed at position ${serverSeed.indexOf(fourBytesHex)}`);
          }
          
          // Check if this appears in custom ID
          if (customId.includes(fourBytesHex)) {
            console.log(`  ✅ Found in custom ID`);
          }
          
          analysis.interpretations.push({
            type: `4_bytes_at_${i}`,
            hex: fourBytesHex,
            u32_le: fourBytesLE,
            u32_be: fourBytesBE,
            in_server_seed: serverSeed.includes(fourBytesHex),
            in_custom_id: customId.includes(fourBytesHex)
          });
        }
        
        // Check for timestamp patterns (Unix timestamps are around 1750000000)
        if (data.length >= 8) {
          const timestamp = data.readBigUInt64LE(0);
          const timestampSeconds = Number(timestamp);
          const blockTime = stageData.transaction.blockTime;
          
          console.log(`Potential timestamp: ${timestampSeconds}`);
          console.log(`Block time: ${blockTime}`);
          console.log(`Difference: ${Math.abs(timestampSeconds - blockTime)} seconds`);
          
          if (Math.abs(timestampSeconds - blockTime) < 3600) { // Within 1 hour
            console.log('  ✅ Could be a timestamp!');
          }
        }
        
        // Check for slot patterns
        const slot = stageData.transaction.slot;
        for (let i = 0; i <= data.length - 8; i++) {
          const u64 = data.readBigUInt64LE(i);
          if (Number(u64) === slot) {
            console.log(`  ✅ Found slot number at position ${i}!`);
          }
        }
      }
    } else {
      // For shorter data, just analyze as-is
      console.log('\n📋 Direct Analysis (no Anchor discriminator):');
      
      if (buffer.length >= 4) {
        const u32_le = buffer.readUInt32LE(0);
        console.log('As u32 LE:', u32_le, `(0x${u32_le.toString(16)})`);
      }
      
      if (buffer.length >= 8) {
        const u64_le = buffer.readBigUInt64LE(0);
        console.log('As u64 LE:', u64_le.toString(), `(0x${u64_le.toString(16)})`);
      }
    }
    
    decodedData[stage] = analysis;
  } else {
    console.log('❌ No instruction found for this stage');
  }
});

// Cross-stage analysis
console.log('\n🔄 CROSS-STAGE ANALYSIS');
console.log('=======================');

// Compare discriminators
const discriminators = {};
stages.forEach(stage => {
  if (decodedData[stage] && decodedData[stage].anchor) {
    discriminators[stage] = decodedData[stage].anchor.discriminator;
  }
});

console.log('Discriminators:');
Object.entries(discriminators).forEach(([stage, disc]) => {
  console.log(`  ${stage}: ${disc}`);
});

// Look for common patterns across stages
console.log('\nLooking for common data patterns...');

const allHexData = stages.map(stage => decodedData[stage]?.hex).filter(Boolean);
const allDataSections = stages.map(stage => decodedData[stage]?.anchor?.data).filter(Boolean);

console.log('All instruction hex data:', allHexData);
console.log('All data sections (after discriminator):', allDataSections);

// Check if any data appears in multiple stages
allDataSections.forEach((data1, i) => {
  allDataSections.forEach((data2, j) => {
    if (i !== j && data1 && data2) {
      // Check for overlapping substrings
      for (let len = 8; len <= Math.min(data1.length, data2.length); len += 2) {
        for (let start = 0; start <= data1.length - len; start += 2) {
          const substring = data1.substr(start, len);
          if (data2.includes(substring)) {
            console.log(`✅ Found common pattern "${substring}" between ${stages[i]} and ${stages[j]}`);
          }
        }
      }
    }
  });
});

// Save the analysis
const fullAnalysis = {
  game_info: gameData.game_info,
  server_seed: gameData.api_snapshots.EndCrashRound.serverSeed,
  decoded_instructions: decodedData,
  cross_stage_analysis: {
    discriminators,
    all_hex_data: allHexData,
    all_data_sections: allDataSections
  }
};

fs.writeFileSync('./instruction-data-analysis.json', JSON.stringify(fullAnalysis, null, 2));
console.log('\n💾 Detailed instruction analysis saved to instruction-data-analysis.json');

console.log('\n📋 SUMMARY:');
console.log(`Stages analyzed: ${Object.keys(decodedData).length}`);
console.log('Discriminators found:', Object.keys(discriminators).length);
console.log('Ready for correlation analysis with server seed!');
