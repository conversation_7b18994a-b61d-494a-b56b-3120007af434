import fs from 'fs';
import { Buffer } from 'buffer';
import crypto from 'crypto';

/**
 * Multi-game analysis to find patterns in CreateCrashRound instruction data
 */

// Load all complete game files
const gameFiles = [
  'complete-game-93be55a8.json',
  'complete-game-acfd826c.json', 
  'complete-game-ad84e15d.json'
];

const games = gameFiles.map(file => {
  try {
    return JSON.parse(fs.readFileSync(`./complete-games/${file}`, 'utf8'));
  } catch (e) {
    console.log(`Could not load ${file}:`, e.message);
    return null;
  }
}).filter(Boolean);

console.log('🎯 MULTI-GAME INSTRUCTION DATA ANALYSIS');
console.log('=======================================');
console.log(`Analyzing ${games.length} complete games`);

const gameAnalysis = [];

// Extract instruction data from each game
games.forEach((game, index) => {
  console.log(`\n📊 GAME ${index + 1}: ${game.game_info.custom_id}`);
  console.log('='.repeat(40));
  
  const createStage = game.stages.CreateCrashRound;
  const instruction = createStage.transaction.transaction.message.instructions.find(
    inst => inst.programId === '6YVBrao9DSLVGk7QsXxQVPbs4XyHVtHw9oSTTXw1otEW'
  );
  
  if (!instruction) {
    console.log('❌ No instruction found');
    return;
  }
  
  const buffer = Buffer.from(instruction.data, 'base64');
  const discriminator = buffer.slice(0, 8);
  const data = buffer.slice(8);
  
  // Extract key values
  const analysis = {
    gameId: game.game_info.custom_id,
    fullGameId: game.game_info.api_game_id,
    serverSeed: game.api_snapshots.EndCrashRound?.serverSeed || 'N/A',
    hashedServerSeed: game.api_snapshots.EndCrashRound?.hashedServerSeed || 'N/A',
    crashMultiplier: game.api_snapshots.EndCrashRound?.crashMultiplier || 'N/A',
    blockTime: createStage.transaction.blockTime,
    slot: createStage.transaction.slot,
    signature: createStage.signature,
    
    // Raw data
    discriminator: discriminator.toString('hex'),
    rawData: data.toString('hex'),
    rawBytes: Array.from(data),
    dataLength: data.length,
    
    // Extracted values
    values: {}
  };
  
  if (data.length >= 4) {
    analysis.values.u32_0 = data.readUInt32LE(0);
    analysis.values.u32_4 = data.length >= 8 ? data.readUInt32LE(4) : null;
    analysis.values.u32_8 = data.length >= 12 ? data.readUInt32LE(8) : null;
  }
  
  if (data.length >= 8) {
    analysis.values.u64_0 = data.readBigUInt64LE(0).toString();
    analysis.values.u64_4 = data.length >= 12 ? data.readBigUInt64LE(4).toString() : null;
  }
  
  if (data.length >= 13) {
    analysis.values.lastByte = data[12];
  }
  
  // Display game info
  console.log('Game ID:', analysis.gameId);
  console.log('Server Seed:', analysis.serverSeed);
  console.log('Crash Multiplier:', analysis.crashMultiplier);
  console.log('Block Time:', analysis.blockTime);
  console.log('Slot:', analysis.slot);
  console.log('Raw Data:', analysis.rawData);
  console.log('Key Values:');
  Object.entries(analysis.values).forEach(([key, value]) => {
    if (value !== null) {
      console.log(`  ${key}: ${value}`);
    }
  });
  
  gameAnalysis.push(analysis);
});

// Cross-game pattern analysis
console.log('\n🔍 CROSS-GAME PATTERN ANALYSIS');
console.log('==============================');

// Check for common discriminators
const discriminators = [...new Set(gameAnalysis.map(g => g.discriminator))];
console.log('Discriminators:', discriminators);
console.log('All games use same discriminator:', discriminators.length === 1 ? '✅ YES' : '❌ NO');

// Check data lengths
const dataLengths = [...new Set(gameAnalysis.map(g => g.dataLength))];
console.log('Data lengths:', dataLengths);
console.log('All games have same data length:', dataLengths.length === 1 ? '✅ YES' : '❌ NO');

// Compare values across games
console.log('\n📊 VALUE COMPARISON ACROSS GAMES:');
console.log('=================================');

const valueKeys = Object.keys(gameAnalysis[0]?.values || {});
valueKeys.forEach(key => {
  console.log(`\n${key}:`);
  gameAnalysis.forEach((game, i) => {
    const value = game.values[key];
    if (value !== null) {
      console.log(`  Game ${i+1} (${game.gameId}): ${value}`);
    }
  });
  
  // Check if values are unique
  const uniqueValues = [...new Set(gameAnalysis.map(g => g.values[key]).filter(v => v !== null))];
  console.log(`  Unique values: ${uniqueValues.length}/${gameAnalysis.length} ${uniqueValues.length === gameAnalysis.length ? '(All different)' : '(Some repeats)'}`);
});

// Look for correlations with server seeds
console.log('\n🎯 SERVER SEED CORRELATION ANALYSIS:');
console.log('====================================');

gameAnalysis.forEach((game, i) => {
  console.log(`\nGame ${i+1} (${game.gameId}):`);
  console.log(`Server Seed: ${game.serverSeed}`);
  
  // Try correlating each extracted value with server seed
  Object.entries(game.values).forEach(([key, value]) => {
    if (value !== null && game.serverSeed !== 'N/A') {
      // Try direct hash
      const valueStr = value.toString();
      const hash = crypto.createHash('sha256').update(valueStr).digest('hex');
      
      if (hash === game.serverSeed) {
        console.log(`  🎉 MATCH! ${key} (${value}) hashes to server seed!`);
      }
      
      // Try with game ID
      const withGameId = crypto.createHash('sha256').update(valueStr + game.gameId).digest('hex');
      if (withGameId === game.serverSeed) {
        console.log(`  🎉 MATCH! ${key} + gameId hashes to server seed!`);
      }
      
      // Check if value appears in server seed
      if (game.serverSeed.includes(valueStr) || game.serverSeed.includes(value.toString(16))) {
        console.log(`  ✅ ${key} (${value}) appears in server seed!`);
      }
    }
  });
});

// Look for patterns in how values change
console.log('\n📈 VALUE PROGRESSION ANALYSIS:');
console.log('==============================');

valueKeys.forEach(key => {
  const values = gameAnalysis.map(g => g.values[key]).filter(v => v !== null);
  if (values.length >= 2) {
    console.log(`\n${key} progression:`);
    
    // Check for arithmetic progression
    const diffs = [];
    for (let i = 1; i < values.length; i++) {
      const diff = Number(values[i]) - Number(values[i-1]);
      diffs.push(diff);
      console.log(`  ${values[i-1]} → ${values[i]} (diff: ${diff})`);
    }
    
    // Check if differences are consistent
    const uniqueDiffs = [...new Set(diffs)];
    if (uniqueDiffs.length === 1) {
      console.log(`  🎯 Arithmetic progression! Constant difference: ${uniqueDiffs[0]}`);
    }
  }
});

// Time-based analysis
console.log('\n⏰ TIME-BASED ANALYSIS:');
console.log('=======================');

gameAnalysis.forEach((game, i) => {
  console.log(`\nGame ${i+1} (${game.gameId}):`);
  console.log(`Block Time: ${game.blockTime} (${new Date(game.blockTime * 1000).toISOString()})`);
  console.log(`Slot: ${game.slot}`);
  
  // Check if any values correlate with timing
  Object.entries(game.values).forEach(([key, value]) => {
    if (value !== null) {
      const numValue = Number(value);
      
      // Check proximity to block time
      if (Math.abs(numValue - game.blockTime) < 3600) {
        console.log(`  🕐 ${key} (${value}) is close to block time!`);
      }
      
      // Check if it matches slot
      if (numValue === game.slot) {
        console.log(`  🎰 ${key} (${value}) matches slot number!`);
      }
    }
  });
});

// Save comprehensive analysis
const fullAnalysis = {
  summary: {
    total_games: gameAnalysis.length,
    discriminators: discriminators,
    data_lengths: dataLengths,
    analysis_timestamp: new Date().toISOString()
  },
  games: gameAnalysis,
  patterns: {
    common_discriminator: discriminators.length === 1,
    consistent_data_length: dataLengths.length === 1,
    value_analysis: valueKeys.map(key => ({
      key,
      values: gameAnalysis.map(g => g.values[key]),
      unique_count: [...new Set(gameAnalysis.map(g => g.values[key]).filter(v => v !== null))].length
    }))
  }
};

fs.writeFileSync('./multi-game-analysis.json', JSON.stringify(fullAnalysis, null, 2));

console.log('\n📋 SUMMARY:');
console.log('===========');
console.log(`✅ Analyzed ${gameAnalysis.length} complete games`);
console.log(`✅ All games use discriminator: ${discriminators[0]}`);
console.log(`✅ All games have ${dataLengths[0]} bytes of data`);
console.log('✅ Extracted and compared all possible values');
console.log('✅ Tested correlations with server seeds');
console.log('✅ Analyzed value progressions and timing');
console.log('\n💾 Detailed analysis saved to multi-game-analysis.json');
console.log('\n🎯 Look for patterns in the value progressions and any correlations found!');
