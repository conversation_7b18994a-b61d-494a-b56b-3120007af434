import fs from "fs";
import path from "path";
import crypto from "crypto";

import { WebSocket } from "ws";
import { Connection, PublicKey } from "@solana/web3.js";

// Import our decoder
import { decodeInstructionData } from './reverse-engineer.js';

const connection = new Connection(
  "https://mainnet.helius-rpc.com/?api-key=cf97e40b-e6c3-46d7-b266-42ea1dbb40b3",
  {
    wsEndpoint:
      "wss://mainnet.helius-rpc.com/?api-key=cf97e40b-e6c3-46d7-b266-42ea1dbb40b3",
  }
);

const programId = new PublicKey("6YVBrao9DSLVGk7QsXxQVPbs4XyHVtHw9oSTTXw1otEW");

// API endpoint for current game data
const CRASH_API_URL = "https://backend.solpump.com/api/v1/crash/game/current";

// Game tracking state - track multiple games by their custom ID
let trackedGames = new Map(); // customId -> gameData

// Helper function to get or create game tracking
function getOrCreateGameTracking(customId) {
  if (!trackedGames.has(customId)) {
    trackedGames.set(customId, {
      gameId: null,
      customId: customId,
      stages: {
        CreateCrashRound: null,
        StartCrashRound: null,
        EndCrashRound: null
      },
      apiSnapshots: {
        CreateCrashRound: null,
        StartCrashRound: null,
        EndCrashRound: null
      },
      startTime: new Date().toISOString()
    });
    console.log(`🆕 Started tracking new game: ${customId}`);
  }
  return trackedGames.get(customId);
}

/**
 * Fetch current game data from the API
 */
async function fetchCurrentGameData() {
  try {
    const response = await fetch(CRASH_API_URL, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
        'Accept': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error fetching game data:', error);
    return null;
  }
}

/**
 * Find API game data that matches the custom ID (first part of UUID)
 * This function is used by the new retry logic, so it should be simpler
 */
async function findMatchingApiGame(customId, stage = 'unknown') {
  console.log(`🔍 Looking for API game matching ${customId} (stage: ${stage})`);

  const apiData = await fetchCurrentGameData();
  if (!apiData) {
    console.log(`❌ No API data returned`);
    return null;
  }

  console.log(`📋 API returned game: ${apiData.id}, state: ${apiData.state}`);

  // Check if the API game ID starts with the custom ID
  if (apiData.id && apiData.id.startsWith(customId)) {
    console.log(`✅ Found matching game: ${apiData.id}`);
    return apiData; // Always return the data if game ID matches
  } else {
    console.log(`❌ API game ${apiData.id} doesn't match ${customId}`);
    return null;
  }
}

/**
 * Add a stage to game tracking with API snapshot
 */
async function addGameStage(stage, transactionData, signature, customId) {
  console.log(`📝 Processing ${stage} for game ${customId}`);

  // Get or create game tracking
  const gameData = getOrCreateGameTracking(customId);

  // For EndCrashRound, we need to retry until we get the finished game with serverSeed
  let apiSnapshot = null;

  if (stage === 'EndCrashRound') {
    console.log(`🎯 EndCrashRound detected - will retry until we get API response with serverSeed`);

    const maxRetries = 30; // Try for up to ~60 seconds (30 * 2s)
    let retryCount = 0;

    while (!apiSnapshot && retryCount < maxRetries) {
      const delay = retryCount === 0 ? 500 : 2000; // First try after 500ms, then 2s intervals
      console.log(`⏱️ Attempt ${retryCount + 1}/${maxRetries} - waiting ${delay}ms...`);
      await new Promise(resolve => setTimeout(resolve, delay));

      console.log(`📡 Fetching API data (attempt ${retryCount + 1})...`);
      const apiData = await fetchCurrentGameData();

      if (apiData) {
        console.log(`📋 API returned game: ${apiData.id}, state: ${apiData.state}`);
        console.log(`🔍 ServerSeed present: ${apiData.serverSeed ? 'YES' : 'NO'}`);

        // Check if this API response contains serverSeed (regardless of game ID match)
        if (apiData.serverSeed) {
          console.log(`🎯 FOUND API RESPONSE WITH serverSeed: ${apiData.serverSeed}`);

          // Now check if it matches our game ID
          if (apiData.id && apiData.id.startsWith(customId)) {
            console.log(`✅ Perfect match! Game ID ${apiData.id} matches ${customId}`);
            apiSnapshot = apiData;
            break;
          } else {
            console.log(`⚠️ ServerSeed found but game ID mismatch: ${apiData.id} vs ${customId}`);
            console.log(`   This might be the finished game we're looking for if API moved to next game`);

            // Check if this could be our finished game by looking at transaction hashes
            const gameData = trackedGames.get(customId);
            if (gameData && gameData.stages.EndCrashRound) {
              const endTxSignature = gameData.stages.EndCrashRound.signature;
              if (apiData.finishGameTransactionHash === endTxSignature) {
                console.log(`🎯 BINGO! Transaction hash matches - this is our finished game!`);
                apiSnapshot = apiData;
                break;
              }
            }
          }
        } else {
          console.log(`❌ No serverSeed in API response yet`);
        }
      } else {
        console.log(`❌ No API data returned`);
      }

      retryCount++;
    }

    if (!apiSnapshot) {
      console.log(`💥 Failed to get API response with serverSeed after ${maxRetries} attempts (${maxRetries * 2} seconds)`);
    } else {
      console.log(`🎉 SUCCESS! Got API response with serverSeed after ${retryCount} attempts`);
      console.log(`🔑 ServerSeed: ${apiSnapshot.serverSeed}`);
    }
  } else {
    // For other stages, just fetch normally
    console.log(`📡 Fetching API data for ${stage}...`);
    apiSnapshot = await findMatchingApiGame(customId, stage);
  }

  if (apiSnapshot) {
    console.log(`✅ Found matching API game: ${apiSnapshot.id}`);
    console.log(`   State: ${apiSnapshot.state}`);
    console.log(`   Hashed Server Seed: ${apiSnapshot.hashedServerSeed}`);

    // Update game ID if not set
    if (!gameData.gameId) {
      gameData.gameId = apiSnapshot.id;
    }
  } else {
    console.log(`⚠️ No matching API game found for ${customId}`);
  }

  // Add the stage data
  gameData.stages[stage] = {
    transaction: transactionData,
    signature: signature,
    timestamp: new Date().toISOString(),
    custom_id: customId
  };

  // Add API snapshot
  gameData.apiSnapshots[stage] = apiSnapshot;

  console.log(`✅ Added ${stage} to game ${customId}`);

  // Save individual transaction file BEFORE processing complete game
  saveIndividualTransactionFile(stage, signature, customId, transactionData, apiSnapshot);

  // Check if game is complete
  if (isGameComplete(gameData)) {
    console.log(`🎉 Game ${customId} is COMPLETE! Saving all data...`);
    saveCompleteGameData(gameData);

    // Remove from tracking to free memory
    trackedGames.delete(customId);
  }
}

/**
 * Save individual transaction file with API data
 */
function saveIndividualTransactionFile(stage, signature, customId, transactionData, apiSnapshot) {
  const filePath = path.join(process.cwd(), "logs", `${stage}-${customId || Date.now()}.json`);
  fs.mkdirSync(path.dirname(filePath), { recursive: true });

  // Create combined data structure
  const combinedData = {
    transaction_info: {
      signature: signature,
      instruction_type: stage,
      custom_id: customId,
      timestamp: new Date().toISOString()
    },
    solana_transaction: transactionData,
    api_snapshot: apiSnapshot,
    correlation: {
      api_game_id: apiSnapshot?.id || null,
      api_state: apiSnapshot?.state || null,
      hashed_server_seed: apiSnapshot?.hashedServerSeed || null,
      server_seed: apiSnapshot?.serverSeed || null, // 🎯 THE ACTUAL SERVER SEED!
      client_seed: apiSnapshot?.clientSeed || null,
      crash_multiplier: apiSnapshot?.crashMultiplier || null,
      transaction_matches_api: apiSnapshot ? (
        apiSnapshot.newGameTransactionHash === signature ||
        apiSnapshot.startGameTransactionHash === signature ||
        apiSnapshot.finishGameTransactionHash === signature
      ) : false
    }
  };

  fs.writeFileSync(filePath, JSON.stringify(combinedData, null, 2));
  console.log(`📁 Transaction + API data saved to: ${filePath}`);

  if (apiSnapshot) {
    console.log(`📊 API State: ${apiSnapshot.state}, Seed: ${apiSnapshot.hashedServerSeed?.substring(0, 16)}...`);
    if (apiSnapshot.serverSeed) {
      console.log(`🔑 ServerSeed captured: ${apiSnapshot.serverSeed.substring(0, 16)}...`);
    }
  } else {
    console.log(`⚠️ No API data captured for this transaction`);
  }
}

/**
 * Check if all 3 stages are captured for a specific game
 */
function isGameComplete(gameData) {
  return gameData.stages.CreateCrashRound !== null &&
         gameData.stages.StartCrashRound !== null &&
         gameData.stages.EndCrashRound !== null;
}

/**
 * Save complete game data to file
 */
function saveCompleteGameData(gameData) {
  const filename = `complete-game-${gameData.customId}.json`;
  const filepath = path.join(process.cwd(), "complete-games", filename);

  // Decode all instructions
  const decodedStages = {};
  for (const [stageName, stageData] of Object.entries(gameData.stages)) {
    if (stageData) {
      const targetInstructions = stageData.transaction.transaction.message.instructions.filter(
        instruction => instruction.programId === programId.toBase58()
      );

      decodedStages[stageName] = {
        ...stageData,
        decoded_instructions: targetInstructions.map(instruction => ({
          raw_data: instruction.data,
          decoded_data: decodeInstructionData(instruction.data),
          accounts: instruction.accounts
        }))
      };
    }
  }

  const completeGameData = {
    game_info: {
      custom_id: gameData.customId,
      api_game_id: gameData.gameId,
      start_time: gameData.startTime,
      completion_time: new Date().toISOString(),
      is_complete: true
    },
    api_snapshots: gameData.apiSnapshots, // API data at each stage
    stages: decodedStages,
    analysis: {
      note: "Complete game data with API snapshots at each stage for reverse engineering server seed",
      stages_captured: Object.keys(gameData.stages).filter(stage => gameData.stages[stage] !== null),
      total_stages: Object.keys(gameData.stages).length,
      api_snapshots_captured: Object.keys(gameData.apiSnapshots).filter(stage => gameData.apiSnapshots[stage] !== null).length
    }
  };

  fs.mkdirSync(path.dirname(filepath), { recursive: true });
  fs.writeFileSync(filepath, JSON.stringify(completeGameData, null, 2));

  console.log(`💾 Complete game data saved to: ${filepath}`);
  console.log(`📊 Captured ${completeGameData.analysis.stages_captured.length} stages and ${completeGameData.analysis.api_snapshots_captured} API snapshots`);

  // Perform server seed analysis on complete game
  performCompleteGameAnalysis(completeGameData);
}

/**
 * Perform comprehensive analysis on complete game data
 */
function performCompleteGameAnalysis(completeGameData) {
  console.log('\n🔬 PERFORMING COMPLETE GAME ANALYSIS');
  console.log('=====================================');

  const stages = completeGameData.stages;
  const apiSnapshots = completeGameData.api_snapshots;

  console.log('Game ID:', completeGameData.game_info.custom_id);
  console.log('API Game ID:', completeGameData.game_info.api_game_id);

  // Show API evolution through stages
  console.log('\n--- API Data Evolution ---');
  for (const [stageName, apiSnapshot] of Object.entries(apiSnapshots)) {
    if (apiSnapshot) {
      console.log(`${stageName}:`);
      console.log(`  State: ${apiSnapshot.state}`);
      console.log(`  Hashed Server Seed: ${apiSnapshot.hashedServerSeed}`);
      console.log(`  Client Seed: ${apiSnapshot.clientSeed || 'null'}`);
      console.log(`  Crash Multiplier: ${apiSnapshot.crashMultiplier || 'null'}`);
      console.log(`  Transaction Hashes:`);
      console.log(`    New: ${apiSnapshot.newGameTransactionHash || 'null'}`);
      console.log(`    Start: ${apiSnapshot.startGameTransactionHash || 'null'}`);
      console.log(`    Finish: ${apiSnapshot.finishGameTransactionHash || 'null'}`);
    }
  }

  // Find the API snapshot with hashed server seed
  let serverSeedSnapshot = null;
  for (const apiSnapshot of Object.values(apiSnapshots)) {
    if (apiSnapshot && apiSnapshot.hashedServerSeed) {
      serverSeedSnapshot = apiSnapshot;
      break;
    }
  }

  if (!serverSeedSnapshot) {
    console.log('❌ No API snapshot with hashed server seed found');
    return;
  }

  console.log('\n--- Server Seed Analysis ---');
  console.log('Using hashed server seed:', serverSeedSnapshot.hashedServerSeed);

  // Analyze each stage against the server seed
  for (const [stageName, stageData] of Object.entries(stages)) {
    if (stageData && stageData.decoded_instructions.length > 0) {
      console.log(`\n--- ${stageName} vs Server Seed ---`);
      const decoded = stageData.decoded_instructions[0].decoded_data;
      console.log('Transaction data hex:', decoded.data_hex || 'No data');

      // Try to find server seed correlation
      if (decoded.data_hex) {
        tryServerSeedCorrelation(decoded, serverSeedSnapshot, stageName);
      }
    }
  }

  // Try combinations of all stages
  console.log('\n--- Multi-Stage Analysis ---');
  tryMultiStageAnalysis(stages, serverSeedSnapshot);

  // Try correlating with API data changes
  console.log('\n--- API Data Correlation ---');
  tryApiDataCorrelation(stages, apiSnapshots, serverSeedSnapshot);
}

/**
 * Try to correlate server seed with individual stage data
 */
function tryServerSeedCorrelation(decodedData, apiData, stageName) {
  const hashedSeed = Buffer.from(apiData.hashedServerSeed, 'base64');
  const instructionDataBuffer = Buffer.from(decodedData.data_hex, 'hex');

  console.log(`Trying ${stageName} data correlation...`);

  // Try direct hash
  const directHash = crypto.createHash('sha256').update(instructionDataBuffer).digest();
  if (directHash.equals(hashedSeed)) {
    console.log(`✅ FOUND! ${stageName} data directly hashes to server seed!`);
    return true;
  }

  // Try with stage name prefix
  const withStage = Buffer.concat([Buffer.from(stageName), instructionDataBuffer]);
  const stageHash = crypto.createHash('sha256').update(withStage).digest();
  if (stageHash.equals(hashedSeed)) {
    console.log(`✅ FOUND! ${stageName} name + data hashes to server seed!`);
    return true;
  }

  return false;
}

/**
 * Try combinations of all stage data
 */
function tryMultiStageAnalysis(stages, apiData) {
  const hashedSeed = Buffer.from(apiData.hashedServerSeed, 'base64');

  // Get all stage data
  const stageBuffers = {};
  for (const [stageName, stageData] of Object.entries(stages)) {
    if (stageData && stageData.decoded_instructions.length > 0) {
      const decoded = stageData.decoded_instructions[0].decoded_data;
      stageBuffers[stageName] = Buffer.from(decoded.data_hex, 'hex');
    }
  }

  // Try concatenating all stage data
  const allStageData = Object.values(stageBuffers);
  if (allStageData.length >= 2) {
    const combined = Buffer.concat(allStageData);
    const combinedHash = crypto.createHash('sha256').update(combined).digest();

    console.log('Combined all stages hash:', combinedHash.toString('base64'));
    console.log('Expected:', apiData.hashedServerSeed);

    if (combinedHash.equals(hashedSeed)) {
      console.log('✅ FOUND! All stage data combined hashes to server seed!');
      return true;
    }
  }

  // Try different combinations
  const stageNames = Object.keys(stageBuffers);
  for (let i = 0; i < stageNames.length; i++) {
    for (let j = i + 1; j < stageNames.length; j++) {
      const combo = Buffer.concat([stageBuffers[stageNames[i]], stageBuffers[stageNames[j]]]);
      const comboHash = crypto.createHash('sha256').update(combo).digest();

      if (comboHash.equals(hashedSeed)) {
        console.log(`✅ FOUND! ${stageNames[i]} + ${stageNames[j]} data hashes to server seed!`);
        return true;
      }
    }
  }

  console.log('❌ No multi-stage correlation found');
  return false;
}

/**
 * Try correlating with API data changes between stages
 */
function tryApiDataCorrelation(stages, apiSnapshots, serverSeedSnapshot) {
  console.log('Analyzing API data changes...');

  // Look for patterns in how API data changes between stages
  const apiStates = [];
  for (const [stageName, apiSnapshot] of Object.entries(apiSnapshots)) {
    if (apiSnapshot) {
      apiStates.push({
        stage: stageName,
        state: apiSnapshot.state,
        hasClientSeed: !!apiSnapshot.clientSeed,
        hasCrashMultiplier: !!apiSnapshot.crashMultiplier,
        transactionHashes: {
          new: apiSnapshot.newGameTransactionHash,
          start: apiSnapshot.startGameTransactionHash,
          finish: apiSnapshot.finishGameTransactionHash
        }
      });
    }
  }

  console.log('API state progression:');
  apiStates.forEach(state => {
    console.log(`  ${state.stage}: ${state.state} (clientSeed: ${state.hasClientSeed}, crashMultiplier: ${state.hasCrashMultiplier})`);
  });

  // The server seed should be consistent across all stages for the same game
  const hashedSeeds = [];
  for (const apiSnapshot of Object.values(apiSnapshots)) {
    if (apiSnapshot && apiSnapshot.hashedServerSeed) {
      hashedSeeds.push(apiSnapshot.hashedServerSeed);
    }
  }

  const uniqueSeeds = [...new Set(hashedSeeds)];
  console.log(`Found ${uniqueSeeds.length} unique hashed server seeds across stages`);

  if (uniqueSeeds.length === 1) {
    console.log('✅ Server seed is consistent across all stages');
  } else {
    console.log('⚠️ Server seed changes between stages:', uniqueSeeds);
  }
}

/**
 * Try to reverse engineer the server seed from transaction data
 */
function analyzeServerSeed(transactionData, gameData) {
  console.log('\n=== SERVER SEED ANALYSIS ===');

  if (!gameData) {
    console.log('No game data available');
    return;
  }

  console.log('Game ID:', gameData.id);
  console.log('Hashed Server Seed:', gameData.hashedServerSeed);
  console.log('Transaction Hash:', gameData.newGameTransactionHash);

  // Decode the hashed server seed from base64
  const hashedSeedBuffer = Buffer.from(gameData.hashedServerSeed, 'base64');
  console.log('Hashed Seed (hex):', hashedSeedBuffer.toString('hex'));
  console.log('Hashed Seed (length):', hashedSeedBuffer.length);

  // Try to find correlations with transaction data
  const targetInstructions = transactionData.transaction.message.instructions.filter(
    instruction => instruction.programId === programId.toBase58()
  );

  if (targetInstructions.length > 0) {
    const instruction = targetInstructions[0];
    const decodedData = decodeInstructionData(instruction.data);

    console.log('\n--- Transaction Data Analysis ---');
    console.log('Instruction:', decodedData.instruction_name);
    console.log('Raw data hex:', decodedData.data_hex);

    // Try different approaches to find the server seed
    tryServerSeedRecovery(decodedData, gameData, transactionData);
  }
}

/**
 * Attempt various methods to recover or correlate the server seed
 */
function tryServerSeedRecovery(decodedData, gameData, transactionData) {
  console.log('\n--- Server Seed Recovery Attempts ---');

  const hashedSeed = Buffer.from(gameData.hashedServerSeed, 'base64');
  const instructionDataBuffer = Buffer.from(decodedData.data_hex, 'hex');

  // Method 1: Check if instruction data directly contains the seed
  console.log('Method 1: Direct seed check');
  const sha256Hash = crypto.createHash('sha256').update(instructionDataBuffer).digest();
  console.log('SHA256 of instruction data:', sha256Hash.toString('base64'));
  console.log('Matches hashed seed?', sha256Hash.equals(hashedSeed));

  // Method 2: Try different parts of the instruction data
  console.log('\nMethod 2: Partial data hashing');
  for (let i = 0; i < instructionDataBuffer.length - 3; i++) {
    for (let len = 4; len <= instructionDataBuffer.length - i; len++) {
      const slice = instructionDataBuffer.slice(i, i + len);
      const hash = crypto.createHash('sha256').update(slice).digest();
      if (hash.equals(hashedSeed)) {
        console.log(`✅ FOUND SEED! Bytes ${i}-${i+len-1}:`, slice.toString('hex'));
        return slice;
      }
    }
  }

  // Method 3: Try combining with other transaction data
  console.log('\nMethod 3: Combined data hashing');
  const signature = transactionData.transaction.signatures[0];
  const signatureBuffer = Buffer.from(signature, 'base64');

  // Try instruction data + signature
  const combined1 = Buffer.concat([instructionDataBuffer, signatureBuffer]);
  const hash1 = crypto.createHash('sha256').update(combined1).digest();
  console.log('Instruction + Signature hash:', hash1.toString('base64'));
  console.log('Matches?', hash1.equals(hashedSeed));

  // Method 4: Try with game ID
  console.log('\nMethod 4: Game ID correlation');
  const gameIdBuffer = Buffer.from(gameData.id.replace(/-/g, ''), 'hex');
  const combined2 = Buffer.concat([instructionDataBuffer, gameIdBuffer]);
  const hash2 = crypto.createHash('sha256').update(combined2).digest();
  console.log('Instruction + Game ID hash:', hash2.toString('base64'));
  console.log('Matches?', hash2.equals(hashedSeed));

  // Method 5: Try with timestamp
  console.log('\nMethod 5: Timestamp correlation');
  const timestamp = new Date(gameData.createdAt).getTime();
  const timestampBuffer = Buffer.alloc(8);
  timestampBuffer.writeBigUInt64LE(BigInt(timestamp));
  const combined3 = Buffer.concat([instructionDataBuffer, timestampBuffer]);
  const hash3 = crypto.createHash('sha256').update(combined3).digest();
  console.log('Instruction + Timestamp hash:', hash3.toString('base64'));
  console.log('Matches?', hash3.equals(hashedSeed));

  console.log('\n❌ No direct correlation found. The server seed might be:');
  console.log('- Generated independently on the server');
  console.log('- Derived from private server data not in the transaction');
  console.log('- Combined with additional entropy not visible on-chain');
}

const ws = new WebSocket(
  "wss://mainnet.helius-rpc.com/?api-key=cf97e40b-e6c3-46d7-b266-42ea1dbb40b3"
);

function sendRequest(ws) {
  const request = {
    jsonrpc: "2.0",
    id: 2,
    method: "logsSubscribe",
    params: [
      {
        mentions: [programId.toBase58()],
      },
      {
        commitment: "confirmed",
        encoding: "jsonParsed",
        transactionDetails: "full",
      },
    ],
  };
  ws.send(JSON.stringify(request));
}

// Function to send a ping to the WebSocket server
function startPing(ws) {
  setInterval(() => {
    if (ws.readyState === WebSocket.OPEN) {
      ws.ping();
      console.log("Ping sent");
    }
  }, 30000); // Ping every 30 seconds
}

// Define WebSocket event handlers

ws.on("open", function open() {
  console.log("WebSocket is open");
  sendRequest(ws); // Send a request once the WebSocket is open
  startPing(ws); // Start sending pings
});

ws.on("message", function incoming(data) {
  const messageStr = data.toString("utf8");
  try {
    const messageObj = JSON.parse(messageStr);
    if (messageObj.method === "logsNotification") {
      const logArray = messageObj.params.result.value.logs;
      const findInstructions = [
        "Program log: Instruction: CreateCrashRound",
        "Program log: Instruction: StartCrashRound",
        "Program log: Instruction: EndCrashRound",
      ];
      //   // we gonna check if one of the findInstructions is in the logArray
      const findInstruction = findInstructions.some((find) =>
        logArray.some((log) => log.includes(find))
      );
      if (findInstruction) {
        console.log("Received:", JSON.stringify(messageObj, null, 2));
        handleLogs(messageObj.params.result.value);
      }
    }
  } catch (e) {
    console.error("Failed to parse JSON:", e);
  }
});

ws.on("error", function error(err) {
  console.error("WebSocket error:", err);
});

ws.on("close", function close() {
  console.log("WebSocket is closed");
});

const handleLogs = async (data) => {
  const signature = data.signature;
  const logArray = data.logs;

  // Find the instruction type
  const instructionTypeRow = logArray.find((log) =>
    log.includes("Program log: Instruction:")
  );
  const instructionType = instructionTypeRow.split(
    "Program log: Instruction:"
  )[1].trim();

  // Extract custom ID from logs - different patterns for different stages
  let customId = null;

  // Try different log patterns
  const customIdLog = logArray.find(log => log.includes('custom ID:'));
  if (customIdLog) {
    customId = customIdLog.split('custom ID: ')[1];
  } else {
    // Try "Round ID" pattern for StartCrashRound and EndCrashRound
    const roundIdLog = logArray.find(log => log.includes('Round ID'));
    if (roundIdLog) {
      // Extract ID from patterns like:
      // "Crash game about to start - Round ID 185e926c"
      // "Round ID 185e926c has ended. New round starting soon."
      const match = roundIdLog.match(/Round ID (\w+)/);
      if (match) {
        customId = match[1];
      }
    }
  }

  console.log(`\n🎮 ${instructionType} Event Detected!`);
  console.log("Custom Game ID:", customId);
  console.log("Transaction Signature:", signature);

  // Get the transaction data
  const transaction = await connection.getParsedTransaction(signature, {
    commitment: "confirmed",
    maxSupportedTransactionVersion: 0,
  });

  if (!transaction) {
    console.log("❌ Could not fetch transaction data");
    return;
  }

  // Handle all stages the same way - add to game tracking with API correlation
  await addGameStage(instructionType, transaction, signature, customId);
};