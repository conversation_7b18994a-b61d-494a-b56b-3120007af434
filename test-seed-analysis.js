import fs from 'fs';
import crypto from 'crypto';
import { decodeInstructionData } from './reverse-engineer.js';

/**
 * Test the server seed analysis with example data
 */
async function testServerSeedAnalysis() {
  console.log('🧪 Testing Server Seed Analysis');
  
  // Load the example transaction
  const exampleTransaction = JSON.parse(fs.readFileSync('./example.json', 'utf8'));
  
  // Get current game data from API
  console.log('📡 Fetching current game data...');
  let gameData;
  try {
    const response = await fetch('https://backend.solpump.com/api/v1/crash/game/current', {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
        'Accept': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const text = await response.text();
    console.log('Raw response:', text.substring(0, 200) + '...');
    gameData = JSON.parse(text);
    
    console.log('\n=== CURRENT GAME DATA ===');
    console.log('Game ID:', gameData.id);
    console.log('State:', gameData.state);
    console.log('Hashed Server Seed:', gameData.hashedServerSeed);
    console.log('Transaction Hash:', gameData.newGameTransactionHash);
    
    // Analyze the example transaction
    console.log('\n=== EXAMPLE TRANSACTION ANALYSIS ===');
    const targetInstructions = exampleTransaction.transaction.message.instructions.filter(
      instruction => instruction.programId === '6YVBrao9DSLVGk7QsXxQVPbs4XyHVtHw9oSTTXw1otEW'
    );
    
    if (targetInstructions.length > 0) {
      const instruction = targetInstructions[0];
      const decodedData = decodeInstructionData(instruction.data);
      
      console.log('Instruction:', decodedData.instruction_name);
      console.log('Data hex:', decodedData.data_hex);
      
      // Try to correlate with server seed patterns
      analyzeServerSeedPatterns(decodedData, gameData, exampleTransaction);
    }
    
  } catch (error) {
    console.error('Error fetching game data:', error);
  }
}

/**
 * Analyze patterns between transaction data and server seed
 */
function analyzeServerSeedPatterns(decodedData, gameData, transactionData) {
  console.log('\n=== SERVER SEED PATTERN ANALYSIS ===');
  
  const hashedSeed = Buffer.from(gameData.hashedServerSeed, 'base64');
  const instructionDataBuffer = Buffer.from(decodedData.data_hex, 'hex');
  
  console.log('Hashed seed (base64):', gameData.hashedServerSeed);
  console.log('Hashed seed (hex):', hashedSeed.toString('hex'));
  console.log('Hashed seed length:', hashedSeed.length);
  
  console.log('\nInstruction data (hex):', decodedData.data_hex);
  console.log('Instruction data length:', instructionDataBuffer.length);
  
  // Try various combinations and transformations
  const attempts = [];
  
  // 1. Direct hash of instruction data
  attempts.push({
    method: 'SHA256(instruction_data)',
    input: instructionDataBuffer,
    hash: crypto.createHash('sha256').update(instructionDataBuffer).digest()
  });
  
  // 2. Hash of different parts of instruction data
  for (let i = 0; i < instructionDataBuffer.length - 3; i++) {
    for (let len = 4; len <= Math.min(16, instructionDataBuffer.length - i); len++) {
      const slice = instructionDataBuffer.slice(i, i + len);
      attempts.push({
        method: `SHA256(bytes_${i}_to_${i+len-1})`,
        input: slice,
        hash: crypto.createHash('sha256').update(slice).digest()
      });
    }
  }
  
  // 3. Try with transaction signature
  const signature = transactionData.transaction.signatures[0];
  const sigBuffer = Buffer.from(signature, 'base64');
  attempts.push({
    method: 'SHA256(instruction_data + signature)',
    input: Buffer.concat([instructionDataBuffer, sigBuffer]),
    hash: crypto.createHash('sha256').update(Buffer.concat([instructionDataBuffer, sigBuffer])).digest()
  });
  
  // 4. Try with block time
  const blockTime = transactionData.blockTime;
  const timeBuffer = Buffer.alloc(8);
  timeBuffer.writeBigUInt64LE(BigInt(blockTime));
  attempts.push({
    method: 'SHA256(instruction_data + block_time)',
    input: Buffer.concat([instructionDataBuffer, timeBuffer]),
    hash: crypto.createHash('sha256').update(Buffer.concat([instructionDataBuffer, timeBuffer])).digest()
  });
  
  // 5. Try with slot
  const slot = transactionData.slot;
  const slotBuffer = Buffer.alloc(8);
  slotBuffer.writeBigUInt64LE(BigInt(slot));
  attempts.push({
    method: 'SHA256(instruction_data + slot)',
    input: Buffer.concat([instructionDataBuffer, slotBuffer]),
    hash: crypto.createHash('sha256').update(Buffer.concat([instructionDataBuffer, slotBuffer])).digest()
  });
  
  // Check for matches
  console.log('\n--- Checking for matches ---');
  let foundMatch = false;
  
  for (const attempt of attempts) {
    const matches = attempt.hash.equals(hashedSeed);
    if (matches) {
      console.log(`✅ MATCH FOUND!`);
      console.log(`Method: ${attempt.method}`);
      console.log(`Input: ${attempt.input.toString('hex')}`);
      console.log(`Hash: ${attempt.hash.toString('base64')}`);
      foundMatch = true;
      break;
    }
  }
  
  if (!foundMatch) {
    console.log('❌ No direct matches found');
    console.log('\nTop 5 attempts:');
    attempts.slice(0, 5).forEach((attempt, i) => {
      console.log(`${i+1}. ${attempt.method}`);
      console.log(`   Input: ${attempt.input.toString('hex')}`);
      console.log(`   Hash: ${attempt.hash.toString('base64')}`);
      console.log(`   Expected: ${gameData.hashedServerSeed}`);
      console.log('');
    });
  }
  
  // Additional analysis
  console.log('\n--- Additional Analysis ---');
  console.log('Game ID from API:', gameData.id);
  console.log('Custom ID from logs:', extractCustomIdFromLogs(transactionData));
  console.log('Transaction matches current game?', gameData.newGameTransactionHash === transactionData.transaction.signatures[0]);
}

function extractCustomIdFromLogs(transactionData) {
  const customIdLog = transactionData.meta.logMessages.find(log => log.includes('custom ID:'));
  return customIdLog ? customIdLog.split('custom ID: ')[1] : null;
}

// Run the test
testServerSeedAnalysis().catch(console.error);
