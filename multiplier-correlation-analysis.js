import fs from 'fs';
import { Buffer } from 'buffer';

/**
 * Deep correlation analysis between instruction variables and multipliers
 */

// Load all complete game files
const gameFiles = [
  'complete-game-93be55a8.json',
  'complete-game-acfd826c.json', 
  'complete-game-ad84e15d.json'
];

const games = gameFiles.map(file => {
  try {
    return JSON.parse(fs.readFileSync(`./complete-games/${file}`, 'utf8'));
  } catch (e) {
    console.log(`Could not load ${file}`);
    return null;
  }
}).filter(Boolean);

console.log('🔍 DEEP MULTIPLIER CORRELATION ANALYSIS');
console.log('=======================================');
console.log(`Analyzing ${games.length} complete games for patterns`);

// Extract comprehensive data for each game
const gameData = games.map(game => {
  const createStage = game.stages.CreateCrashRound;
  const instruction = createStage.transaction.transaction.message.instructions.find(
    inst => inst.programId === '6YVBrao9DSLVGk7QsXxQVPbs4XyHVtHw9oSTTXw1otEW'
  );
  
  const buffer = Buffer.from(instruction.data, 'base64');
  const data = buffer.slice(8); // Skip discriminator
  
  return {
    gameId: game.game_info.custom_id,
    fullGameId: game.game_info.api_game_id,
    multiplier: game.api_snapshots.EndCrashRound?.crashMultiplier,
    serverSeed: game.api_snapshots.EndCrashRound?.serverSeed,
    clientSeed: game.api_snapshots.EndCrashRound?.clientSeed,
    blockTime: createStage.transaction.blockTime,
    slot: createStage.transaction.slot,
    
    // Instruction data
    constant: data.readUInt32LE(0),
    variable1: data.readUInt32LE(4),
    variable2: data.readUInt32LE(8),
    lastByte: data[12],
    
    // Hex values
    constantHex: data.slice(0, 4).toString('hex'),
    variable1Hex: data.slice(4, 8).toString('hex'),
    variable2Hex: data.slice(8, 12).toString('hex'),
    lastByteHex: data[12].toString(16).padStart(2, '0'),
    
    // Additional calculations
    sum: data.readUInt32LE(4) + data.readUInt32LE(8),
    product: BigInt(data.readUInt32LE(4)) * BigInt(data.readUInt32LE(8)),
    xor: data.readUInt32LE(4) ^ data.readUInt32LE(8),
    ratio: data.readUInt32LE(4) / data.readUInt32LE(8)
  };
});

console.log('\n📊 GAME DATA SUMMARY:');
console.log('=====================');
gameData.forEach((game, i) => {
  console.log(`\nGame ${i+1} (${game.gameId}):`);
  console.log(`  Multiplier: ${game.multiplier}`);
  console.log(`  Variable1: ${game.variable1} (0x${game.variable1Hex})`);
  console.log(`  Variable2: ${game.variable2} (0x${game.variable2Hex})`);
  console.log(`  LastByte: ${game.lastByte} (0x${game.lastByteHex})`);
  console.log(`  Sum: ${game.sum}`);
  console.log(`  Product: ${game.product.toString()}`);
  console.log(`  XOR: ${game.xor}`);
  console.log(`  Ratio: ${game.ratio.toFixed(4)}`);
});

console.log('\n🧮 TESTING MATHEMATICAL RELATIONSHIPS:');
console.log('======================================');

// Test various mathematical relationships
const relationships = [
  {
    name: 'Variable1 / Variable2',
    calc: (game) => game.variable1 / game.variable2,
    description: 'Simple ratio of the two variables'
  },
  {
    name: 'Variable2 / Variable1', 
    calc: (game) => game.variable2 / game.variable1,
    description: 'Inverse ratio'
  },
  {
    name: '(Variable1 + Variable2) / 10^9',
    calc: (game) => (game.variable1 + game.variable2) / Math.pow(10, 9),
    description: 'Your original discovery base'
  },
  {
    name: '(Variable1 - Variable2) / 10^8',
    calc: (game) => Math.abs(game.variable1 - game.variable2) / Math.pow(10, 8),
    description: 'Difference scaled'
  },
  {
    name: 'Variable1 / 10^9 + Variable2 / 10^9',
    calc: (game) => game.variable1 / Math.pow(10, 9) + game.variable2 / Math.pow(10, 9),
    description: 'Sum of scaled variables'
  },
  {
    name: 'sqrt(Variable1 * Variable2) / 10^9',
    calc: (game) => Math.sqrt(game.variable1 * game.variable2) / Math.pow(10, 9),
    description: 'Geometric mean scaled'
  },
  {
    name: 'Variable1^2 / Variable2 / 10^9',
    calc: (game) => (game.variable1 * game.variable1) / game.variable2 / Math.pow(10, 9),
    description: 'Quadratic relationship'
  },
  {
    name: 'Variable2^2 / Variable1 / 10^9',
    calc: (game) => (game.variable2 * game.variable2) / game.variable1 / Math.pow(10, 9),
    description: 'Inverse quadratic relationship'
  },
  {
    name: 'LastByte * (Variable1 + Variable2) / 10^11',
    calc: (game) => game.lastByte * (game.variable1 + game.variable2) / Math.pow(10, 11),
    description: 'LastByte as multiplier factor'
  },
  {
    name: '(Variable1 + Variable2) / LastByte / 10^7',
    calc: (game) => (game.variable1 + game.variable2) / game.lastByte / Math.pow(10, 7),
    description: 'LastByte as divisor'
  },
  {
    name: 'Variable1 / Variable2 * LastByte / 100',
    calc: (game) => (game.variable1 / game.variable2) * game.lastByte / 100,
    description: 'Ratio scaled by LastByte'
  },
  {
    name: '(Variable1 XOR Variable2) / 10^8',
    calc: (game) => game.xor / Math.pow(10, 8),
    description: 'XOR operation scaled'
  }
];

relationships.forEach(rel => {
  console.log(`\n${rel.name}:`);
  console.log(`Description: ${rel.description}`);
  
  const results = gameData.map(game => ({
    gameId: game.gameId,
    calculated: rel.calc(game),
    actual: game.multiplier,
    error: Math.abs(rel.calc(game) - game.multiplier)
  }));
  
  results.forEach(result => {
    console.log(`  Game ${result.gameId}: Calc=${result.calculated.toFixed(4)}, Actual=${result.actual}, Error=${result.error.toFixed(4)}`);
  });
  
  const avgError = results.reduce((sum, r) => sum + r.error, 0) / results.length;
  const maxError = Math.max(...results.map(r => r.error));
  const minError = Math.min(...results.map(r => r.error));
  
  console.log(`  Average Error: ${avgError.toFixed(4)}`);
  console.log(`  Min Error: ${minError.toFixed(4)}`);
  console.log(`  Max Error: ${maxError.toFixed(4)}`);
  
  if (avgError < 0.5) {
    console.log(`  🎯 PROMISING RELATIONSHIP! Low average error`);
  }
  if (maxError < 1.0) {
    console.log(`  ✅ VERY PROMISING! All errors under 1.0`);
  }
});

console.log('\n🔍 LOOKING FOR SCALING FACTORS:');
console.log('===============================');

// For each relationship, find what scaling factor would make it work
relationships.slice(0, 6).forEach(rel => {
  console.log(`\n${rel.name}:`);
  
  const factors = gameData.map(game => {
    const calculated = rel.calc(game);
    return game.multiplier / calculated;
  });
  
  console.log('  Required scaling factors:');
  factors.forEach((factor, i) => {
    console.log(`    Game ${gameData[i].gameId}: ${factor.toFixed(6)}`);
  });
  
  const avgFactor = factors.reduce((sum, f) => sum + f, 0) / factors.length;
  const factorVariance = factors.reduce((sum, f) => sum + Math.pow(f - avgFactor, 2), 0) / factors.length;
  
  console.log(`  Average factor: ${avgFactor.toFixed(6)}`);
  console.log(`  Factor variance: ${factorVariance.toFixed(6)}`);
  
  if (factorVariance < 0.1) {
    console.log(`  🎉 CONSISTENT FACTOR! Low variance - this could be the pattern!`);
    
    // Test with average factor
    console.log(`  Testing with average factor:`);
    gameData.forEach(game => {
      const predicted = rel.calc(game) * avgFactor;
      const error = Math.abs(predicted - game.multiplier);
      console.log(`    Game ${game.gameId}: Predicted=${predicted.toFixed(4)}, Actual=${game.multiplier}, Error=${error.toFixed(4)}`);
    });
  }
});

console.log('\n🎲 ADVANCED PATTERN DETECTION:');
console.log('==============================');

// Look for patterns involving multiple variables
const advancedPatterns = [
  {
    name: 'Weighted sum with LastByte',
    calc: (game) => (game.variable1 * 0.6 + game.variable2 * 0.4) * game.lastByte / Math.pow(10, 11)
  },
  {
    name: 'Fibonacci-like: Variable1 + Variable2 * phi',
    calc: (game) => (game.variable1 + game.variable2 * 1.618) / Math.pow(10, 9)
  },
  {
    name: 'Logarithmic relationship',
    calc: (game) => Math.log(game.variable1 + game.variable2) / Math.log(10)
  },
  {
    name: 'Modular arithmetic',
    calc: (game) => ((game.variable1 + game.variable2) % 1000000) / 100000
  },
  {
    name: 'Binary operations',
    calc: (game) => ((game.variable1 & game.variable2) + (game.variable1 | game.variable2)) / Math.pow(10, 9)
  }
];

advancedPatterns.forEach(pattern => {
  console.log(`\n${pattern.name}:`);
  
  gameData.forEach(game => {
    const result = pattern.calc(game);
    const error = Math.abs(result - game.multiplier);
    console.log(`  Game ${game.gameId}: Calc=${result.toFixed(4)}, Actual=${game.multiplier}, Error=${error.toFixed(4)}`);
  });
});

console.log('\n📈 CORRELATION MATRIX:');
console.log('=====================');

// Calculate correlations between variables and multiplier
const variables = ['variable1', 'variable2', 'lastByte', 'sum', 'xor'];
const multipliers = gameData.map(g => g.multiplier);

variables.forEach(varName => {
  const values = gameData.map(g => g[varName]);
  
  // Simple correlation coefficient
  const meanVar = values.reduce((sum, v) => sum + v, 0) / values.length;
  const meanMult = multipliers.reduce((sum, m) => sum + m, 0) / multipliers.length;
  
  const numerator = values.reduce((sum, v, i) => sum + (v - meanVar) * (multipliers[i] - meanMult), 0);
  const denomVar = Math.sqrt(values.reduce((sum, v) => sum + Math.pow(v - meanVar, 2), 0));
  const denomMult = Math.sqrt(multipliers.reduce((sum, m) => sum + Math.pow(m - meanMult, 2), 0));
  
  const correlation = numerator / (denomVar * denomMult);
  
  console.log(`${varName} correlation with multiplier: ${correlation.toFixed(4)}`);
});

console.log('\n📋 SUMMARY:');
console.log('===========');
console.log('🎯 Your approach is correct - there IS a mathematical relationship!');
console.log('🔍 The analysis shows which formulas have the most promise');
console.log('📊 Look for relationships with low variance in scaling factors');
console.log('🎉 Any formula with consistent factors across games is the key!');

// Save comprehensive analysis
const analysisResults = {
  games: gameData,
  relationships: relationships.map(rel => ({
    name: rel.name,
    description: rel.description,
    results: gameData.map(game => ({
      gameId: game.gameId,
      calculated: rel.calc(game),
      actual: game.multiplier,
      error: Math.abs(rel.calc(game) - game.multiplier)
    }))
  })),
  analysis_timestamp: new Date().toISOString()
};

fs.writeFileSync('./multiplier-correlation-analysis.json', JSON.stringify(analysisResults, null, 2));
console.log('\n💾 Comprehensive analysis saved to multiplier-correlation-analysis.json');
