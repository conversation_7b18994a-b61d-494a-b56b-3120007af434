import fs from 'fs';
import { Buffer } from 'buffer';
import crypto from 'crypto';

/**
 * Try hashing various combinations of instruction data to generate the server seed
 */

// Load all complete game files
const gameFiles = [
  'complete-game-93be55a8.json',
  'complete-game-acfd826c.json', 
  'complete-game-ad84e15d.json'
];

const games = gameFiles.map(file => {
  try {
    return JSON.parse(fs.readFileSync(`./complete-games/${file}`, 'utf8'));
  } catch (e) {
    console.log(`Could not load ${file}`);
    return null;
  }
}).filter(Boolean);

console.log('🔍 HASHING INSTRUCTION DATA TO FIND SERVER SEED');
console.log('===============================================');
console.log(`Analyzing ${games.length} complete games`);

// Extract data for each game
const gameData = games.map(game => {
  const createStage = game.stages.CreateCrashRound;
  const instruction = createStage.transaction.transaction.message.instructions.find(
    inst => inst.programId === '6YVBrao9DSLVGk7QsXxQVPbs4XyHVtHw9oSTTXw1otEW'
  );
  
  const buffer = Buffer.from(instruction.data, 'base64');
  const discriminator = buffer.slice(0, 8);
  const data = buffer.slice(8);
  
  return {
    gameId: game.game_info.custom_id,
    fullGameId: game.game_info.api_game_id,
    serverSeed: game.api_snapshots.EndCrashRound?.serverSeed,
    clientSeed: game.api_snapshots.EndCrashRound?.clientSeed,
    blockTime: createStage.transaction.blockTime,
    slot: createStage.transaction.slot,
    signature: createStage.signature,
    
    // Raw data
    discriminator: discriminator.toString('hex'),
    fullData: buffer.toString('hex'),
    instructionData: data.toString('hex'),
    
    // Parsed values
    constant: data.readUInt32LE(0),
    variable1: data.readUInt32LE(4),
    variable2: data.readUInt32LE(8),
    lastByte: data[12],
    
    // Hex components
    constantHex: data.slice(0, 4).toString('hex'),
    variable1Hex: data.slice(4, 8).toString('hex'),
    variable2Hex: data.slice(8, 12).toString('hex'),
    lastByteHex: data[12].toString(16).padStart(2, '0'),
    variablePartHex: data.slice(4).toString('hex')
  };
});

// Function to try hashing various inputs
function tryHashCombinations(game) {
  console.log(`\n🎯 GAME ${game.gameId}:`);
  console.log(`Target Server Seed: ${game.serverSeed}`);
  console.log('='.repeat(50));
  
  const attempts = [];
  
  // 1. Hash individual components
  const components = [
    { name: 'constant', value: game.constant.toString() },
    { name: 'variable1', value: game.variable1.toString() },
    { name: 'variable2', value: game.variable2.toString() },
    { name: 'lastByte', value: game.lastByte.toString() },
    { name: 'constantHex', value: game.constantHex },
    { name: 'variable1Hex', value: game.variable1Hex },
    { name: 'variable2Hex', value: game.variable2Hex },
    { name: 'lastByteHex', value: game.lastByteHex },
    { name: 'instructionData', value: game.instructionData },
    { name: 'variablePartHex', value: game.variablePartHex },
    { name: 'fullData', value: game.fullData },
    { name: 'discriminator', value: game.discriminator }
  ];
  
  components.forEach(comp => {
    const hash = crypto.createHash('sha256').update(comp.value, 'utf8').digest('hex');
    attempts.push({ method: `SHA256(${comp.name})`, input: comp.value, output: hash });
    
    if (hash === game.serverSeed) {
      console.log(`🎉 BREAKTHROUGH! SHA256(${comp.name}) = server seed!`);
      console.log(`   Input: ${comp.value}`);
      console.log(`   Output: ${hash}`);
    }
  });
  
  // 2. Hash combinations of components
  const combinations = [
    { name: 'constant + variable1', value: game.constant.toString() + game.variable1.toString() },
    { name: 'constant + variable2', value: game.constant.toString() + game.variable2.toString() },
    { name: 'variable1 + variable2', value: game.variable1.toString() + game.variable2.toString() },
    { name: 'variable1 + variable2 + lastByte', value: game.variable1.toString() + game.variable2.toString() + game.lastByte.toString() },
    { name: 'constantHex + variable1Hex', value: game.constantHex + game.variable1Hex },
    { name: 'constantHex + variable2Hex', value: game.constantHex + game.variable2Hex },
    { name: 'variable1Hex + variable2Hex', value: game.variable1Hex + game.variable2Hex },
    { name: 'variable1Hex + variable2Hex + lastByteHex', value: game.variable1Hex + game.variable2Hex + game.lastByteHex },
    { name: 'all variables concatenated', value: game.variable1.toString() + game.variable2.toString() + game.lastByte.toString() },
    { name: 'all hex concatenated', value: game.variable1Hex + game.variable2Hex + game.lastByteHex }
  ];
  
  combinations.forEach(combo => {
    const hash = crypto.createHash('sha256').update(combo.value, 'utf8').digest('hex');
    attempts.push({ method: `SHA256(${combo.name})`, input: combo.value, output: hash });
    
    if (hash === game.serverSeed) {
      console.log(`🎉 BREAKTHROUGH! SHA256(${combo.name}) = server seed!`);
      console.log(`   Input: ${combo.value}`);
      console.log(`   Output: ${hash}`);
    }
  });
  
  // 3. Hash with game context
  const contextCombinations = [
    { name: 'instructionData + gameId', value: game.instructionData + game.gameId },
    { name: 'instructionData + fullGameId', value: game.instructionData + game.fullGameId },
    { name: 'variable2Hex + gameId', value: game.variable2Hex + game.gameId },
    { name: 'variable2Hex + fullGameId', value: game.variable2Hex + game.fullGameId },
    { name: 'variablePartHex + gameId', value: game.variablePartHex + game.gameId },
    { name: 'variablePartHex + fullGameId', value: game.variablePartHex + game.fullGameId },
    { name: 'instructionData + blockTime', value: game.instructionData + game.blockTime.toString() },
    { name: 'instructionData + slot', value: game.instructionData + game.slot.toString() },
    { name: 'instructionData + signature', value: game.instructionData + game.signature },
    { name: 'variable2 + blockTime', value: game.variable2.toString() + game.blockTime.toString() },
    { name: 'variable2 + slot', value: game.variable2.toString() + game.slot.toString() }
  ];
  
  contextCombinations.forEach(combo => {
    const hash = crypto.createHash('sha256').update(combo.value, 'utf8').digest('hex');
    attempts.push({ method: `SHA256(${combo.name})`, input: combo.value.substring(0, 100) + '...', output: hash });
    
    if (hash === game.serverSeed) {
      console.log(`🎉 BREAKTHROUGH! SHA256(${combo.name}) = server seed!`);
      console.log(`   Input: ${combo.value}`);
      console.log(`   Output: ${hash}`);
    }
  });
  
  // 4. Try different hash algorithms
  const hashAlgorithms = ['md5', 'sha1', 'sha256', 'sha512'];
  hashAlgorithms.forEach(algo => {
    try {
      const hash = crypto.createHash(algo).update(game.instructionData, 'hex').digest('hex');
      attempts.push({ method: `${algo.toUpperCase()}(instructionData)`, input: game.instructionData, output: hash });
      
      if (hash === game.serverSeed) {
        console.log(`🎉 BREAKTHROUGH! ${algo.toUpperCase()}(instructionData) = server seed!`);
        console.log(`   Input: ${game.instructionData}`);
        console.log(`   Output: ${hash}`);
      }
      
      // Try with variable part only
      const hash2 = crypto.createHash(algo).update(game.variablePartHex, 'hex').digest('hex');
      if (hash2 === game.serverSeed) {
        console.log(`🎉 BREAKTHROUGH! ${algo.toUpperCase()}(variablePartHex) = server seed!`);
        console.log(`   Input: ${game.variablePartHex}`);
        console.log(`   Output: ${hash2}`);
      }
    } catch (e) {
      // Skip unsupported algorithms
    }
  });
  
  // 5. Try HMAC with different keys
  const hmacKeys = [
    game.gameId,
    game.fullGameId,
    game.constantHex,
    game.discriminator,
    'solpump',
    'crash',
    'game'
  ];
  
  hmacKeys.forEach(key => {
    const hmac = crypto.createHmac('sha256', key).update(game.instructionData, 'hex').digest('hex');
    attempts.push({ method: `HMAC-SHA256(instructionData, "${key}")`, input: game.instructionData, output: hmac });
    
    if (hmac === game.serverSeed) {
      console.log(`🎉 BREAKTHROUGH! HMAC-SHA256(instructionData, "${key}") = server seed!`);
      console.log(`   Input: ${game.instructionData}`);
      console.log(`   Key: ${key}`);
      console.log(`   Output: ${hmac}`);
    }
  });
  
  // 6. Try with binary data (not hex string)
  const binaryAttempts = [
    { name: 'instructionData as binary', data: Buffer.from(game.instructionData, 'hex') },
    { name: 'variablePartHex as binary', data: Buffer.from(game.variablePartHex, 'hex') },
    { name: 'variable2Hex as binary', data: Buffer.from(game.variable2Hex, 'hex') }
  ];
  
  binaryAttempts.forEach(attempt => {
    const hash = crypto.createHash('sha256').update(attempt.data).digest('hex');
    attempts.push({ method: `SHA256(${attempt.name})`, input: attempt.data.toString('hex'), output: hash });
    
    if (hash === game.serverSeed) {
      console.log(`🎉 BREAKTHROUGH! SHA256(${attempt.name}) = server seed!`);
      console.log(`   Input: ${attempt.data.toString('hex')}`);
      console.log(`   Output: ${hash}`);
    }
  });
  
  // 7. Check for partial matches (first 16 characters)
  console.log('\n🔍 Checking for partial matches (first 16 chars):');
  const partialMatches = attempts.filter(attempt => 
    attempt.output.substring(0, 16) === game.serverSeed.substring(0, 16)
  );
  
  if (partialMatches.length > 0) {
    console.log('✅ Found partial matches:');
    partialMatches.forEach(match => {
      console.log(`   ${match.method}: ${match.output.substring(0, 16)}...`);
    });
  } else {
    console.log('❌ No partial matches found');
  }
  
  return attempts;
}

// Analyze each game
const allAttempts = [];
gameData.forEach(game => {
  const attempts = tryHashCombinations(game);
  allAttempts.push({ gameId: game.gameId, attempts });
});

console.log('\n📊 CROSS-GAME PATTERN ANALYSIS:');
console.log('===============================');

// Look for patterns across games
const methodCounts = {};
allAttempts.forEach(gameAttempts => {
  gameAttempts.attempts.forEach(attempt => {
    if (!methodCounts[attempt.method]) {
      methodCounts[attempt.method] = [];
    }
    methodCounts[attempt.method].push(attempt.output);
  });
});

// Check if any method produces consistent patterns
Object.entries(methodCounts).forEach(([method, outputs]) => {
  const uniqueOutputs = [...new Set(outputs)];
  if (uniqueOutputs.length < outputs.length) {
    console.log(`🔍 ${method} produces some repeated outputs (${uniqueOutputs.length}/${outputs.length} unique)`);
  }
});

console.log('\n📋 SUMMARY:');
console.log('===========');
console.log(`✅ Tested ${Object.keys(methodCounts).length} different hashing methods`);
console.log(`✅ Analyzed ${gameData.length} complete games`);
console.log('✅ Checked individual components, combinations, and context data');
console.log('✅ Tried multiple hash algorithms (MD5, SHA1, SHA256, SHA512)');
console.log('✅ Tested HMAC with various keys');
console.log('✅ Analyzed both string and binary inputs');

console.log('\n🎯 If no matches were found:');
console.log('- Server seed might be generated with additional server-side entropy');
console.log('- Complex multi-step process might be involved');
console.log('- Timing or external factors might influence generation');
console.log('- Server seed might be pre-generated and referenced by instruction data');

// Save detailed results
const analysisResults = {
  summary: {
    total_games: gameData.length,
    total_methods_tested: Object.keys(methodCounts).length,
    analysis_timestamp: new Date().toISOString()
  },
  games: allAttempts,
  method_counts: methodCounts
};

fs.writeFileSync('./hash-to-serverseed-analysis.json', JSON.stringify(analysisResults, null, 2));
console.log('\n💾 Detailed analysis saved to hash-to-serverseed-analysis.json');
