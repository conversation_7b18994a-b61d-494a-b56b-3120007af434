{"game_info": {"custom_id": "93be55a8", "api_game_id": "93be55a8-b790-4fad-87bb-2900eb874415", "start_time": "2025-06-24T06:48:09.955Z", "completion_time": "2025-06-24T06:49:00.628Z", "is_complete": true}, "server_seed": "58254f60f9661ee71d6c1514ec774914df925d9da9f5550524d43a12a0ed46db", "decoded_instructions": {"CreateCrashRound": {"base64": "2A148kSFA6tqVKD9LGu8kNBNTZ2X", "hex": "d80d78f2448503ab6a54a0fd2c6bbc90d04d4d9d97", "length": 21, "bytes": [216, 13, 120, 242, 68, 133, 3, 171, 106, 84, 160, 253, 44, 107, 188, 144, 208, 77, 77, 157, 151], "interpretations": [{"type": "bytes", "value": [106, 84, 160, 253, 44, 107, 188, 144, 208, 77, 77, 157, 151]}, {"type": "u32_le", "value": 4255143018, "hex": "fda0546a"}, {"type": "u64_le", "value": "10429328678061102186", "hex": "90bc6b2cfda0546a"}, {"type": "4_bytes_at_0", "hex": "6a54a0fd", "u32_le": 4255143018, "u32_be": 1783931133, "in_server_seed": false, "in_custom_id": false}, {"type": "4_bytes_at_1", "hex": "54a0fd2c", "u32_le": 754819156, "u32_be": 1419836716, "in_server_seed": false, "in_custom_id": false}, {"type": "4_bytes_at_2", "hex": "a0fd2c6b", "u32_le": 1798110624, "u32_be": 2700946539, "in_server_seed": false, "in_custom_id": false}, {"type": "4_bytes_at_3", "hex": "fd2c6bbc", "u32_le": 3161140477, "u32_be": 4247546812, "in_server_seed": false, "in_custom_id": false}, {"type": "4_bytes_at_4", "hex": "2c6bbc90", "u32_le": 2428267308, "u32_be": 745258128, "in_server_seed": false, "in_custom_id": false}, {"type": "4_bytes_at_5", "hex": "6bbc90d0", "u32_le": 3499146347, "u32_be": 1807519952, "in_server_seed": false, "in_custom_id": false}, {"type": "4_bytes_at_6", "hex": "bc90d04d", "u32_le": 1305514172, "u32_be": 3163607117, "in_server_seed": false, "in_custom_id": false}, {"type": "4_bytes_at_7", "hex": "90d04d4d", "u32_le": 1296945296, "u32_be": 2429570381, "in_server_seed": false, "in_custom_id": false}, {"type": "4_bytes_at_8", "hex": "d04d4d9d", "u32_le": 2639089104, "u32_be": 3494727069, "in_server_seed": false, "in_custom_id": false}, {"type": "4_bytes_at_9", "hex": "4d4d9d97", "u32_le": 2543668557, "u32_be": 1296932247, "in_server_seed": false, "in_custom_id": false}], "anchor": {"discriminator": "d80d78f2448503ab", "data": "6a54a0fd2c6bbc90d04d4d9d97", "data_length": 13}}, "StartCrashRound": {"base64": "44Yc2DLQ5xq", "hex": "e3861cd832d0e71a", "length": 8, "bytes": [227, 134, 28, 216, 50, 208, 231, 26], "interpretations": [], "anchor": {"discriminator": "e3861cd832d0e71a", "data": "", "data_length": 0}}, "EndCrashRound": {"base64": "fVvnPShDm2Q", "hex": "7d5be73d28439b64", "length": 8, "bytes": [125, 91, 231, 61, 40, 67, 155, 100], "interpretations": [], "anchor": {"discriminator": "7d5be73d28439b64", "data": "", "data_length": 0}}}, "cross_stage_analysis": {"discriminators": {"CreateCrashRound": "d80d78f2448503ab", "StartCrashRound": "e3861cd832d0e71a", "EndCrashRound": "7d5be73d28439b64"}, "all_hex_data": ["d80d78f2448503ab6a54a0fd2c6bbc90d04d4d9d97", "e3861cd832d0e71a", "7d5be73d28439b64"], "all_data_sections": ["6a54a0fd2c6bbc90d04d4d9d97"]}}