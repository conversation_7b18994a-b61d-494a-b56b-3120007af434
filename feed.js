import { Connection, PublicKey } from '@solana/web3.js';
import { <PERSON><PERSON><PERSON> } from 'buffer';

/**
 * Live feed showing CreateCrashRound events with predicted multipliers
 * Based on our breakthrough discovery: LastByte / 40 ≈ Multiplier
 */

const connection = new Connection('https://mainnet.helius-rpc.com/?api-key=cf97e40b-e6c3-46d7-b266-42ea1dbb40b3', 'confirmed');
const PROGRAM_ID = new PublicKey('6YVBrao9DSLVGk7QsXxQVPbs4XyHVtHw9oSTTXw1otEW');

// Our breakthrough formula
function predictMultiplier(lastByte) {
  return (lastByte / 40).toFixed(2);
}

// Alternative formulas to test
function getAlternativePredictions(variable1, variable2, lastByte) {
  return {
    formula1: (lastByte / 40).toFixed(2),
    formula2: (lastByte / 39.5).toFixed(2),
    formula3: ((lastByte + 5) / 40).toFixed(2),
    weighted: ((variable1 * 0.8 + variable2 * 0.2) * lastByte / Math.pow(10, 11)).toFixed(2),
    modular: (((variable1 + variable2) % 1000000) / 100000).toFixed(2)
  };
}

console.log('🎯 CRASH GAME MULTIPLIER PREDICTION FEED');
console.log('========================================');
console.log('Monitoring CreateCrashRound events...');
console.log('Using breakthrough formula: LastByte / 40');
console.log('');

let gameCount = 0;

// Monitor logs for the program
connection.onLogs(PROGRAM_ID, (logs, context) => {
  try {
    // Look for CreateCrashRound in the logs
    const hasCreateCrashRound = logs.logs.some(log => 
      log.includes('custom ID:') || 
      log.includes('CreateCrashRound') ||
      log.includes('Round ID')
    );
    
    if (hasCreateCrashRound) {
      // Get the transaction details
      connection.getTransaction(logs.signature, {
        commitment: 'confirmed',
        maxSupportedTransactionVersion: 0
      }).then(transaction => {
        if (!transaction) return;
        
        // Find the instruction for our program
        const instruction = transaction.transaction.message.instructions.find(
          inst => inst.programId.equals(PROGRAM_ID)
        );
        
        if (instruction && instruction.data) {
          const buffer = Buffer.from(instruction.data, 'base64');
          
          // Check if this is CreateCrashRound (21 bytes total, 13 bytes data after discriminator)
          if (buffer.length === 21) {
            const discriminator = buffer.slice(0, 8);
            const data = buffer.slice(8);
            
            // Verify it's CreateCrashRound discriminator
            if (discriminator.toString('hex') === 'd80d78f2448503ab') {
              gameCount++;
              
              // Extract the values
              const constant = data.readUInt32LE(0);
              const variable1 = data.readUInt32LE(4);
              const variable2 = data.readUInt32LE(8);
              const lastByte = data[12];
              
              // Extract game ID from logs
              let gameId = 'unknown';
              const customIdLog = logs.logs.find(log => log.includes('custom ID:'));
              if (customIdLog) {
                const match = customIdLog.match(/custom ID:\s*([a-f0-9]+)/i);
                if (match) {
                  gameId = match[1];
                }
              }
              
              // Get predictions
              const mainPrediction = predictMultiplier(lastByte);
              const alternatives = getAlternativePredictions(variable1, variable2, lastByte);
              
              // Output the prediction
              console.log(`🎮 Game #${gameCount}`);
              console.log(`{`);
              console.log(`  id: "${gameId}",`);
              console.log(`  expected: "${mainPrediction}"`);
              console.log(`}`);
              console.log(`📊 Details:`);
              console.log(`  Variable1: ${variable1}`);
              console.log(`  Variable2: ${variable2}`);
              console.log(`  LastByte: ${lastByte}`);
              console.log(`  Signature: ${logs.signature}`);
              console.log(`  Block Time: ${transaction.blockTime}`);
              console.log(`📈 Alternative Predictions:`);
              console.log(`  LastByte/40: ${alternatives.formula1}`);
              console.log(`  LastByte/39.5: ${alternatives.formula2}`);
              console.log(`  (LastByte+5)/40: ${alternatives.formula3}`);
              console.log(`  Weighted: ${alternatives.weighted}`);
              console.log(`  Modular: ${alternatives.modular}`);
              console.log(`⏰ ${new Date().toISOString()}`);
              console.log('─'.repeat(50));
            }
          }
        }
      }).catch(error => {
        console.error('Error fetching transaction:', error.message);
      });
    }
  } catch (error) {
    console.error('Error processing logs:', error.message);
  }
}, 'confirmed');

// Keep the process running
process.on('SIGINT', () => {
  console.log('\n🛑 Stopping multiplier prediction feed...');
  console.log(`📊 Total games predicted: ${gameCount}`);
  process.exit(0);
});

console.log('✅ Feed started! Press Ctrl+C to stop');
console.log('🔍 Waiting for CreateCrashRound events...');
console.log('');
