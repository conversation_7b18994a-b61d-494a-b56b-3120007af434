import fs from 'fs';
import { Buffer } from 'buffer';

/**
 * Detailed analysis of the most promising values from CreateCrashRound data
 */

// Load the complete game data
const gameData = JSON.parse(fs.readFileSync('./complete-games/complete-game-93be55a8.json', 'utf8'));

// Extract the CreateCrashRound data
const createStage = gameData.stages.CreateCrashRound;
const instruction = createStage.transaction.transaction.message.instructions.find(
  inst => inst.programId === '6YVBrao9DSLVGk7QsXxQVPbs4XyHVtHw9oSTTXw1otEW'
);

const buffer = Buffer.from(instruction.data, 'base64');
const data = buffer.slice(8); // The 13 bytes after discriminator

console.log('🎯 DETAILED ANALYSIS OF CreateCrashRound DATA VALUES');
console.log('===================================================');
console.log('Raw Data (13 bytes):', data.toString('hex'));
console.log('Game Info:');
console.log('  Custom ID:', gameData.game_info.custom_id);
console.log('  Full Game ID:', gameData.game_info.api_game_id);
console.log('  Server Seed:', gameData.api_snapshots.EndCrashRound.serverSeed);
console.log('  Crash Multiplier:', gameData.api_snapshots.EndCrashRound.crashMultiplier);
console.log('  Block Time:', createStage.transaction.blockTime);
console.log('  Slot:', createStage.transaction.slot);

console.log('\n📊 ALL POSSIBLE VALUES EXTRACTED FROM THE 13 BYTES:');
console.log('===================================================');

// Individual bytes
console.log('\n1️⃣ INDIVIDUAL BYTES:');
for (let i = 0; i < data.length; i++) {
  const byte = data[i];
  console.log(`  Byte ${i}: ${byte} (0x${byte.toString(16).padStart(2, '0')}) (binary: ${byte.toString(2).padStart(8, '0')})`);
}

// 16-bit values
console.log('\n2️⃣ 16-BIT VALUES (Little Endian):');
for (let i = 0; i <= data.length - 2; i++) {
  const u16 = data.readUInt16LE(i);
  console.log(`  Bytes ${i}-${i+1}: ${u16} (0x${u16.toString(16)})`);
}

// 32-bit values
console.log('\n3️⃣ 32-BIT VALUES (Little Endian):');
for (let i = 0; i <= data.length - 4; i++) {
  const u32 = data.readUInt32LE(i);
  const i32 = data.readInt32LE(i);
  const float = data.readFloatLE(i);
  
  console.log(`  Bytes ${i}-${i+3}:`);
  console.log(`    Unsigned: ${u32} (0x${u32.toString(16)})`);
  console.log(`    Signed: ${i32}`);
  console.log(`    Float: ${float}`);
  console.log(`    As timestamp: ${new Date(u32 * 1000).toISOString()} (if Unix timestamp)`);
  console.log(`    Diff from block time: ${Math.abs(u32 - createStage.transaction.blockTime)} seconds`);
}

// 64-bit values
console.log('\n4️⃣ 64-BIT VALUES (Little Endian):');
for (let i = 0; i <= data.length - 8; i++) {
  const u64 = data.readBigUInt64LE(i);
  const i64 = data.readBigInt64LE(i);
  const double = data.readDoubleLE(i);
  
  console.log(`  Bytes ${i}-${i+7}:`);
  console.log(`    Unsigned: ${u64.toString()}`);
  console.log(`    Signed: ${i64.toString()}`);
  console.log(`    Double: ${double}`);
  console.log(`    As timestamp (ms): ${new Date(Number(u64)).toISOString()} (if Unix timestamp in ms)`);
  console.log(`    As timestamp (us): ${new Date(Number(u64) / 1000).toISOString()} (if Unix timestamp in microseconds)`);
  console.log(`    Matches slot: ${Number(u64) === createStage.transaction.slot ? 'YES' : 'NO'}`);
}

// Hex sequences
console.log('\n5️⃣ HEX SEQUENCES:');
for (let len = 4; len <= data.length; len += 2) {
  for (let start = 0; start <= data.length - len; start++) {
    const sequence = data.slice(start, start + len).toString('hex');
    console.log(`  Bytes ${start}-${start+len-1}: ${sequence}`);
  }
}

// Structured interpretations
console.log('\n6️⃣ STRUCTURED INTERPRETATIONS:');

// Structure 1: u32 + u64 + u8
if (data.length >= 13) {
  const struct1 = {
    field1_u32: data.readUInt32LE(0),
    field2_u64: data.readBigUInt64LE(4).toString(),
    field3_u8: data[12]
  };
  console.log('  Structure 1 (u32 + u64 + u8):');
  console.log(`    Field 1 (u32): ${struct1.field1_u32} (0x${struct1.field1_u32.toString(16)})`);
  console.log(`    Field 2 (u64): ${struct1.field2_u64}`);
  console.log(`    Field 3 (u8): ${struct1.field3_u8} (0x${struct1.field3_u8.toString(16)})`);
}

// Structure 2: u64 + u32 + u8
if (data.length >= 13) {
  const struct2 = {
    field1_u64: data.readBigUInt64LE(0).toString(),
    field2_u32: data.readUInt32LE(8),
    field3_u8: data[12]
  };
  console.log('  Structure 2 (u64 + u32 + u8):');
  console.log(`    Field 1 (u64): ${struct2.field1_u64}`);
  console.log(`    Field 2 (u32): ${struct2.field2_u32} (0x${struct2.field2_u32.toString(16)})`);
  console.log(`    Field 3 (u8): ${struct2.field3_u8} (0x${struct2.field3_u8.toString(16)})`);
}

// Structure 3: Three u32 values + u8
if (data.length >= 13) {
  const struct3 = {
    field1_u32: data.readUInt32LE(0),
    field2_u32: data.readUInt32LE(4),
    field3_u32: data.readUInt32LE(8),
    field4_u8: data[12]
  };
  console.log('  Structure 3 (u32 + u32 + u32 + u8):');
  console.log(`    Field 1 (u32): ${struct3.field1_u32} (0x${struct3.field1_u32.toString(16)})`);
  console.log(`    Field 2 (u32): ${struct3.field2_u32} (0x${struct3.field2_u32.toString(16)})`);
  console.log(`    Field 3 (u32): ${struct3.field3_u32} (0x${struct3.field3_u32.toString(16)})`);
  console.log(`    Field 4 (u8): ${struct3.field4_u8} (0x${struct3.field4_u8.toString(16)})`);
}

// Pattern analysis
console.log('\n7️⃣ PATTERN ANALYSIS:');

// Check for repeating bytes
const byteCounts = {};
for (let i = 0; i < data.length; i++) {
  const byte = data[i];
  byteCounts[byte] = (byteCounts[byte] || 0) + 1;
}

console.log('  Byte frequency:');
Object.entries(byteCounts).forEach(([byte, count]) => {
  if (count > 1) {
    console.log(`    Byte ${byte} (0x${parseInt(byte).toString(16)}) appears ${count} times`);
  }
});

// Check for arithmetic progressions
console.log('  Arithmetic patterns:');
for (let i = 0; i < data.length - 2; i++) {
  const diff1 = data[i+1] - data[i];
  const diff2 = data[i+2] - data[i+1];
  if (diff1 === diff2 && diff1 !== 0) {
    console.log(`    Arithmetic progression at bytes ${i}-${i+2}: ${data[i]}, ${data[i+1]}, ${data[i+2]} (diff: ${diff1})`);
  }
}

// Game-specific correlations
console.log('\n8️⃣ GAME-SPECIFIC CORRELATIONS:');

const gameId = gameData.game_info.custom_id;
const serverSeed = gameData.api_snapshots.EndCrashRound.serverSeed;
const crashMultiplier = gameData.api_snapshots.EndCrashRound.crashMultiplier;

console.log(`  Game ID: ${gameId}`);
console.log(`  Server Seed: ${serverSeed}`);
console.log(`  Crash Multiplier: ${crashMultiplier}`);

// Check if any extracted values correlate with known game data
const allValues = [];

// Add all 32-bit values
for (let i = 0; i <= data.length - 4; i++) {
  allValues.push({
    type: `u32_at_${i}`,
    value: data.readUInt32LE(i),
    hex: data.readUInt32LE(i).toString(16)
  });
}

// Add all 64-bit values
for (let i = 0; i <= data.length - 8; i++) {
  allValues.push({
    type: `u64_at_${i}`,
    value: data.readBigUInt64LE(i).toString(),
    hex: data.readBigUInt64LE(i).toString(16)
  });
}

console.log('\n  Checking correlations:');
allValues.forEach(val => {
  const checks = [];
  
  // Check if hex appears in game ID
  if (gameId.includes(val.hex)) {
    checks.push(`appears in game ID`);
  }
  
  // Check if hex appears in server seed
  if (serverSeed.includes(val.hex)) {
    checks.push(`appears in server seed`);
  }
  
  // Check if value is close to crash multiplier
  if (typeof val.value === 'number' && Math.abs(val.value - crashMultiplier) < 1) {
    checks.push(`close to crash multiplier`);
  }
  
  // Check if value is close to block time
  if (typeof val.value === 'number' && Math.abs(val.value - createStage.transaction.blockTime) < 3600) {
    checks.push(`close to block time`);
  }
  
  // Check if value matches slot
  if (val.value.toString() === createStage.transaction.slot.toString()) {
    checks.push(`matches slot number`);
  }
  
  if (checks.length > 0) {
    console.log(`    ${val.type} (${val.value}): ${checks.join(', ')}`);
  }
});

console.log('\n📋 SUMMARY OF ALL EXTRACTED VALUES:');
console.log('===================================');
console.log('Raw hex data:', data.toString('hex'));
console.log('Individual bytes:', Array.from(data));
console.log('As string (if printable):', data.toString('ascii').replace(/[^\x20-\x7E]/g, '.'));
console.log('Length:', data.length, 'bytes');

console.log('\nMost likely interpretations:');
console.log('1. Game parameters for crash round creation');
console.log('2. Seed material (but not the final server seed)');
console.log('3. Timestamp or slot-related data');
console.log('4. Random values for game initialization');
console.log('5. Encoded game configuration');

console.log('\n💡 For reverse engineering, focus on:');
console.log('- The u32 values: 4255143018, 2428267308, 2639089104');
console.log('- The u64 values: 10429328678061102186, 11334801395338210092');
console.log('- The final byte: 151');
console.log('- Any patterns or correlations with game timing/randomness');
