{"game_info": {"custom_id": "testgame1", "api_game_id": "test-game-123", "start_time": "2025-06-24T06:23:15.742Z", "completion_time": "2025-06-24T06:23:15.744Z", "is_complete": true}, "api_data": {"id": "test-game-123", "state": "accepting bets", "hashedServerSeed": "iF+JU5v9a9DEDH/bodn0/XbQG9V9wiOgprLn/ppeMNM=", "clientSeed": null, "newGameTransactionHash": "mock-create-tx-hash"}, "stages": {"CreateCrashRound": {"transaction": {"transaction": {"message": {"instructions": [{"programId": "6YVBrao9DSLVGk7QsXxQVPbs4XyHVtHw9oSTTXw1otEW", "data": "2A148kSFA6tqVKD9LQTWYTXq2SbP", "accounts": ["account1", "account2"]}]}, "signatures": ["mock-create-tx-hash"]}, "meta": {"logMessages": ["Program log: Instruction: CreateCrashRound", "Program log: New round started with custom ID: testgame1"]}}, "signature": "mock-create-tx-hash", "timestamp": "2025-06-24T06:23:15.744Z", "custom_id": "testgame1", "decoded_instructions": [{"raw_data": "2A148kSFA6tqVKD9LQTWYTXq2SbP", "decoded_data": {"instruction_name": "CreateCrashRound", "discriminator_hex": "d80d78f2448503ab", "data_hex": "6a54a0fd2d04d66135ead926cf", "data_bytes": [106, 84, 160, 253, 45, 4, 214, 97, 53, 234, 217, 38, 207], "data_length": 13, "parsed_data": {"raw_bytes": [106, 84, 160, 253, 45, 4, 214, 97, 53, 234, 217, 38, 207], "interpretations": [{"field": "first_4_bytes", "hex": "6a54a0fd", "u32_le": **********, "u32_be": **********}, {"field": "first_8_bytes", "hex": "6a54a0fd2d04d661", "u64_le": "7049826862270272618", "u64_be": "7661925875306518113"}], "possible_structure": {"timestamp_u64_le": "7049826862270272618", "remaining_5_bytes": "35ead926cf", "u32_field": **********, "u64_field": "2799526158383055917", "u8_field": 207}}}, "accounts": ["account1", "account2"]}]}, "StartCrashRound": {"transaction": {"transaction": {"message": {"instructions": [{"programId": "6YVBrao9DSLVGk7QsXxQVPbs4XyHVtHw9oSTTXw1otEW", "data": "3B259lTGB7urWLE8MRUWYUXr3TcQ", "accounts": ["account1", "account2"]}]}, "signatures": ["mock-start-tx-hash"]}, "meta": {"logMessages": ["Program log: Instruction: StartCrashRound", "Program log: Game started with custom ID: testgame1"]}}, "signature": "mock-start-tx-hash", "timestamp": "2025-06-24T06:23:15.744Z", "custom_id": "testgame1", "decoded_instructions": [{"raw_data": "3B259lTGB7urWLE8MRUWYUXr3TcQ", "decoded_data": {"instruction_name": "Unknown", "instruction_type": 220, "raw_data": [220, 29, 185, 246, 84, 198, 7, 187, 171, 88, 177, 60, 49, 21, 22, 97, 69, 235, 221, 55, 16], "hex_data": "dc1db9f654c607bbab58b13c3115166145ebdd3710", "length": 21, "possible_8byte_discriminator": {"discriminator_hex": "dc1db9f654c607bb", "discriminator_bytes": [220, 29, 185, 246, 84, 198, 7, 187], "remaining_data_hex": "ab58b13c3115166145ebdd3710", "remaining_data_bytes": [171, 88, 177, 60, 49, 21, 22, 97, 69, 235, 221, 55, 16]}}, "accounts": ["account1", "account2"]}]}, "EndCrashRound": {"transaction": {"transaction": {"message": {"instructions": [{"programId": "6YVBrao9DSLVGk7QsXxQVPbs4XyHVtHw9oSTTXw1otEW", "data": "4C369mUHC8vsXMF9NSVXZVYs4UdR", "accounts": ["account1", "account2"]}]}, "signatures": ["mock-end-tx-hash"]}, "meta": {"logMessages": ["Program log: Instruction: EndCrashRound", "Program log: Game ended with custom ID: testgame1, multiplier: 2.45"]}}, "signature": "mock-end-tx-hash", "timestamp": "2025-06-24T06:23:15.744Z", "custom_id": "testgame1", "decoded_instructions": [{"raw_data": "4C369mUHC8vsXMF9NSVXZVYs4UdR", "decoded_data": {"instruction_name": "Unknown", "instruction_type": 224, "raw_data": [224, 45, 250, 246, 101, 7, 11, 203, 236, 92, 193, 125, 53, 37, 87, 101, 86, 44, 225, 71, 81], "hex_data": "e02dfaf665070bcbec5cc17d35255765562ce14751", "length": 21, "possible_8byte_discriminator": {"discriminator_hex": "e02dfaf665070bcb", "discriminator_bytes": [224, 45, 250, 246, 101, 7, 11, 203], "remaining_data_hex": "ec5cc17d35255765562ce14751", "remaining_data_bytes": [236, 92, 193, 125, 53, 37, 87, 101, 86, 44, 225, 71, 81]}}, "accounts": ["account1", "account2"]}]}}, "analysis": {"note": "Test complete game data for reverse engineering server seed", "stages_captured": ["CreateCrashRound", "StartCrashRound", "EndCrashRound"], "total_stages": 3}}