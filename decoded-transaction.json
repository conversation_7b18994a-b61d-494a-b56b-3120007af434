{"transaction_signature": "3Jj7nEKceKCSi8R5j5CuF1eWbLe4S3RxgBnVj5sBekr4ENcEd3iQeAdoMsQn1n48ectKvBqdzHcXLsSm9sp911xm", "block_time": **********, "slot": *********, "program_id": "6YVBrao9DSLVGk7QsXxQVPbs4XyHVtHw9oSTTXw1otEW", "decoded_instructions": [{"instruction_index": 0, "accounts": ["rw7igePyZ5r7RmY1nuPU8CdorrHkrMic5sfchbwbjK8", "EwsWeAvhBGr5vf5VWhqjy1hC2Z9guyaiLz2ErDy34rUZ"], "raw_data": "2A148kSFA6tqVKD9LQTWYTXq2SbP", "decoded_data": {"instruction_name": "Unknown", "instruction_type": 216, "raw_data": [216, 13, 120, 242, 68, 133, 3, 171, 106, 84, 160, 253, 45, 4, 214, 97, 53, 234, 217, 38, 207], "hex_data": "d80d78f2448503ab6a54a0fd2d04d66135ead926cf", "length": 21, "possible_8byte_discriminator": {"discriminator_hex": "d80d78f2448503ab", "discriminator_bytes": [216, 13, 120, 242, 68, 133, 3, 171], "remaining_data_hex": "6a54a0fd2d04d66135ead926cf", "remaining_data_bytes": [106, 84, 160, 253, 45, 4, 214, 97, 53, 234, 217, 38, 207]}}, "detailed_analysis": {"raw_hex": "d80d78f2448503ab6a54a0fd2d04d66135ead926cf", "raw_bytes": [216, 13, 120, 242, 68, 133, 3, 171, 106, 84, 160, 253, 45, 4, 214, 97, 53, 234, 217, 38, 207], "expected_custom_id": "fce5bfc6", "length": 21, "anchor_style": {"discriminator_hex": "d80d78f2448503ab", "discriminator_bytes": [216, 13, 120, 242, 68, 133, 3, 171], "data_hex": "6a54a0fd2d04d66135ead926cf", "data_bytes": [106, 84, 160, 253, 45, 4, 214, 97, 53, 234, 217, 38, 207], "data_length": 13}, "data_interpretations": [{"type": "raw_bytes", "value": [106, 84, 160, 253, 45, 4, 214, 97, 53, 234, 217, 38, 207]}, {"type": "u32_little_endian", "value": 4255143018}, {"type": "u64_little_endian", "value": "7049826862270272618"}, {"type": "4_bytes_at_offset_0", "hex": "6a54a0fd", "u32_le": 4255143018, "u32_be": 1783931133, "u32_le_hex": "fda0546a", "u32_be_hex": "6a54a0fd"}, {"type": "4_bytes_at_offset_1", "hex": "54a0fd2d", "u32_le": 771596372, "u32_be": 1419836717, "u32_le_hex": "2dfda054", "u32_be_hex": "54a0fd2d"}, {"type": "4_bytes_at_offset_2", "hex": "a0fd2d04", "u32_le": 70122912, "u32_be": 2700946692, "u32_le_hex": "042dfda0", "u32_be_hex": "a0fd2d04"}, {"type": "4_bytes_at_offset_3", "hex": "fd2d04d6", "u32_le": 3590598141, "u32_be": 4247586006, "u32_le_hex": "d6042dfd", "u32_be_hex": "fd2d04d6"}, {"type": "4_bytes_at_offset_4", "hex": "2d04d661", "u32_le": 1641415725, "u32_be": 755291745, "u32_le_hex": "61d6042d", "u32_be_hex": "2d04d661"}, {"type": "4_bytes_at_offset_5", "hex": "04d66135", "u32_le": 895604228, "u32_be": 81158453, "u32_le_hex": "3561d604", "u32_be_hex": "04d66135"}, {"type": "4_bytes_at_offset_6", "hex": "d66135ea", "u32_le": 3929366998, "u32_be": 3596695018, "u32_le_hex": "ea3561d6", "u32_be_hex": "d66135ea"}, {"type": "4_bytes_at_offset_7", "hex": "6135ead9", "u32_le": 3656004961, "u32_be": 1630923481, "u32_le_hex": "d9ea3561", "u32_be_hex": "6135ead9"}, {"type": "4_bytes_at_offset_8", "hex": "35ead926", "u32_le": 651815477, "u32_be": 904583462, "u32_le_hex": "26d9ea35", "u32_be_hex": "35ead926"}, {"type": "4_bytes_at_offset_9", "hex": "ead926cf", "u32_le": 3475429866, "u32_be": 3940099791, "u32_le_hex": "cf26d9ea", "u32_be_hex": "ead926cf"}]}}], "log_messages": ["Program ComputeBudget111111111111111111111111111111 invoke [1]", "Program ComputeBudget111111111111111111111111111111 success", "Program ComputeBudget111111111111111111111111111111 invoke [1]", "Program ComputeBudget111111111111111111111111111111 success", "Program 6YVBrao9DSLVGk7QsXxQVPbs4XyHVtHw9oSTTXw1otEW invoke [1]", "Program log: Instruction: CreateCrashRound", "Program log: New round started with custom ID: fce5bfc6", "Program 6YVBrao9DSLVGk7QsXxQVPbs4XyHVtHw9oSTTXw1otEW consumed 4206 of 4657 compute units", "Program 6YVBrao9DSLVGk7QsXxQVPbs4XyHVtHw9oSTTXw1otEW success"]}