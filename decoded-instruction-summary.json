{"game_id": "93be55a8", "server_seed": "58254f60f9661ee71d6c1514ec774914df925d9da9f5550524d43a12a0ed46db", "crash_multiplier": 3.72, "decoded_instructions": {"CreateCrashRound": {"total_bytes": 21, "discriminator": "d80d78f2448503ab", "data_bytes": 13, "data_hex": "6a54a0fd2c6bbc90d04d4d9d97", "decoded_structure": {"constant": {"value": 4255143018, "hex": "6a54a0fd", "description": "Program constant (same across games)"}, "variable1": {"value": 2428267308, "hex": "2c6bbc90", "description": "Game-specific parameter 1"}, "variable2": {"value": 2639089104, "hex": "d04d4d9d", "description": "Game-specific parameter 2"}, "last_byte": {"value": 151, "hex": "97", "description": "Game-specific parameter 3 - FOUND IN SERVER SEED!"}}}, "StartCrashRound": {"total_bytes": 8, "discriminator": "e3861cd832d0e71a", "data_bytes": 0, "description": "No parameters - just triggers game start"}, "EndCrashRound": {"total_bytes": 8, "discriminator": "7d5be73d28439b64", "data_bytes": 0, "description": "No parameters - just triggers game end"}}, "correlation_found": {"field": "CreateCrashRound.last_byte", "value": 151, "hex": "97", "appears_in_server_seed": true, "position_in_seed": 20}}