import fs from 'fs';
import { Buffer } from 'buffer';

/**
 * Check if the formula (variable1 + variable2) / 10^9 * 0.744 = multiplier
 */

// Load all complete game files
const gameFiles = [
  'complete-game-93be55a8.json',
  'complete-game-acfd826c.json', 
  'complete-game-ad84e15d.json'
];

const games = gameFiles.map(file => {
  try {
    return JSON.parse(fs.readFileSync(`./complete-games/${file}`, 'utf8'));
  } catch (e) {
    console.log(`Could not load ${file}`);
    return null;
  }
}).filter(Boolean);

console.log('🎯 CHECKING MULTIPLIER FORMULA DISCOVERY');
console.log('========================================');
console.log('Formula: (variable1 + variable2) / 10^9 * 0.744 = multiplier');
console.log(`Analyzing ${games.length} complete games`);

// Extract data for each game
const gameAnalysis = games.map(game => {
  const createStage = game.stages.CreateCrashRound;
  const instruction = createStage.transaction.transaction.message.instructions.find(
    inst => inst.programId === '6YVBrao9DSLVGk7QsXxQVPbs4XyHVtHw9oSTTXw1otEW'
  );
  
  const buffer = Buffer.from(instruction.data, 'base64');
  const data = buffer.slice(8); // Skip discriminator
  
  return {
    gameId: game.game_info.custom_id,
    actualMultiplier: game.api_snapshots.EndCrashRound?.crashMultiplier,
    variable1: data.readUInt32LE(4),
    variable2: data.readUInt32LE(8),
    lastByte: data[12],
    serverSeed: game.api_snapshots.EndCrashRound?.serverSeed
  };
});

console.log('\n📊 FORMULA VERIFICATION:');
console.log('========================');

gameAnalysis.forEach((game, i) => {
  console.log(`\nGame ${i+1} (${game.gameId}):`);
  console.log(`  Actual Multiplier: ${game.actualMultiplier}`);
  console.log(`  Variable1: ${game.variable1}`);
  console.log(`  Variable2: ${game.variable2}`);
  console.log(`  Variable1 + Variable2: ${game.variable1 + game.variable2}`);
  
  // Test the original formula
  const sum = game.variable1 + game.variable2;
  const divided = sum / Math.pow(10, 9);
  const predicted = divided * 0.744;
  
  console.log(`  (${game.variable1} + ${game.variable2}) / 10^9 = ${divided}`);
  console.log(`  ${divided} * 0.744 = ${predicted}`);
  console.log(`  Predicted Multiplier: ${predicted.toFixed(4)}`);
  console.log(`  Actual Multiplier: ${game.actualMultiplier}`);
  console.log(`  Difference: ${Math.abs(predicted - game.actualMultiplier).toFixed(4)}`);
  console.log(`  Match: ${Math.abs(predicted - game.actualMultiplier) < 0.1 ? '✅ CLOSE!' : '❌ No'}`);
});

console.log('\n🔍 TESTING VARIATIONS OF THE FORMULA:');
console.log('=====================================');

// Test different variations
const variations = [
  { name: 'Original: (v1 + v2) / 10^9 * 0.744', calc: (v1, v2) => (v1 + v2) / Math.pow(10, 9) * 0.744 },
  { name: 'Variation 1: (v1 + v2) / 10^8 * 0.744', calc: (v1, v2) => (v1 + v2) / Math.pow(10, 8) * 0.744 },
  { name: 'Variation 2: (v1 + v2) / 10^9 * 0.8', calc: (v1, v2) => (v1 + v2) / Math.pow(10, 9) * 0.8 },
  { name: 'Variation 3: (v1 + v2) / 10^9 * 1.0', calc: (v1, v2) => (v1 + v2) / Math.pow(10, 9) * 1.0 },
  { name: 'Variation 4: (v1 + v2) / 10^9', calc: (v1, v2) => (v1 + v2) / Math.pow(10, 9) },
  { name: 'Variation 5: v1 / 10^9 * multiplier_factor', calc: (v1, v2, actual) => v1 / Math.pow(10, 9) },
  { name: 'Variation 6: v2 / 10^9 * multiplier_factor', calc: (v1, v2, actual) => v2 / Math.pow(10, 9) },
  { name: 'Variation 7: (v1 * v2) / 10^18', calc: (v1, v2) => (v1 * v2) / Math.pow(10, 18) },
  { name: 'Variation 8: (v1 + v2) / 10^9 * 0.5', calc: (v1, v2) => (v1 + v2) / Math.pow(10, 9) * 0.5 }
];

variations.forEach(variation => {
  console.log(`\n${variation.name}:`);
  
  let totalError = 0;
  let perfectMatches = 0;
  let closeMatches = 0;
  
  gameAnalysis.forEach((game, i) => {
    const predicted = variation.calc(game.variable1, game.variable2, game.actualMultiplier);
    const error = Math.abs(predicted - game.actualMultiplier);
    totalError += error;
    
    if (error < 0.01) perfectMatches++;
    if (error < 0.5) closeMatches++;
    
    console.log(`  Game ${i+1}: Predicted=${predicted.toFixed(4)}, Actual=${game.actualMultiplier}, Error=${error.toFixed(4)}`);
  });
  
  const avgError = totalError / gameAnalysis.length;
  console.log(`  Average Error: ${avgError.toFixed(4)}`);
  console.log(`  Perfect Matches (< 0.01): ${perfectMatches}/${gameAnalysis.length}`);
  console.log(`  Close Matches (< 0.5): ${closeMatches}/${gameAnalysis.length}`);
  
  if (perfectMatches === gameAnalysis.length) {
    console.log(`  🎉 PERFECT FORMULA FOUND!`);
  } else if (closeMatches === gameAnalysis.length) {
    console.log(`  ✅ CLOSE FORMULA - might need fine-tuning`);
  }
});

console.log('\n🧮 REVERSE ENGINEERING THE MULTIPLIER FACTOR:');
console.log('=============================================');

// Calculate what the multiplier factor should be for each game
gameAnalysis.forEach((game, i) => {
  const sum = game.variable1 + game.variable2;
  const divided = sum / Math.pow(10, 9);
  const requiredFactor = game.actualMultiplier / divided;
  
  console.log(`\nGame ${i+1} (${game.gameId}):`);
  console.log(`  (v1 + v2) / 10^9 = ${divided.toFixed(6)}`);
  console.log(`  Required factor to get ${game.actualMultiplier}: ${requiredFactor.toFixed(6)}`);
});

// Calculate average factor
const factors = gameAnalysis.map(game => {
  const sum = game.variable1 + game.variable2;
  const divided = sum / Math.pow(10, 9);
  return game.actualMultiplier / divided;
});

const avgFactor = factors.reduce((sum, f) => sum + f, 0) / factors.length;
console.log(`\nAverage multiplier factor: ${avgFactor.toFixed(6)}`);

// Test with the average factor
console.log(`\n🎯 TESTING WITH AVERAGE FACTOR (${avgFactor.toFixed(6)}):`);
gameAnalysis.forEach((game, i) => {
  const sum = game.variable1 + game.variable2;
  const predicted = (sum / Math.pow(10, 9)) * avgFactor;
  const error = Math.abs(predicted - game.actualMultiplier);
  
  console.log(`Game ${i+1}: Predicted=${predicted.toFixed(4)}, Actual=${game.actualMultiplier}, Error=${error.toFixed(4)}`);
});

console.log('\n📋 SUMMARY:');
console.log('===========');
console.log('Your discovery shows a mathematical relationship between instruction data and multiplier!');
console.log('The formula structure (variable1 + variable2) / 10^9 * factor appears promising.');
console.log('Fine-tuning the factor might lead to accurate multiplier prediction!');

// Save analysis
const formulaAnalysis = {
  original_formula: '(variable1 + variable2) / 10^9 * 0.744',
  games: gameAnalysis.map(game => ({
    gameId: game.gameId,
    actualMultiplier: game.actualMultiplier,
    variable1: game.variable1,
    variable2: game.variable2,
    sum: game.variable1 + game.variable2,
    divided: (game.variable1 + game.variable2) / Math.pow(10, 9),
    predicted_original: ((game.variable1 + game.variable2) / Math.pow(10, 9)) * 0.744,
    required_factor: game.actualMultiplier / ((game.variable1 + game.variable2) / Math.pow(10, 9))
  })),
  average_factor: avgFactor,
  analysis_timestamp: new Date().toISOString()
};

fs.writeFileSync('./multiplier-formula-analysis.json', JSON.stringify(formulaAnalysis, null, 2));
console.log('\n💾 Analysis saved to multiplier-formula-analysis.json');
