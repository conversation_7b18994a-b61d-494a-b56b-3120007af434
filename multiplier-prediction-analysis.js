import fs from 'fs';
import { Buffer } from 'buffer';
import { createHmac } from 'crypto';

/**
 * Analyze if we can predict crash multiplier from instruction data
 */

// Load all complete game files
const gameFiles = [
  'complete-game-93be55a8.json',
  'complete-game-acfd826c.json', 
  'complete-game-ad84e15d.json'
];

const games = gameFiles.map(file => {
  try {
    return JSON.parse(fs.readFileSync(`./complete-games/${file}`, 'utf8'));
  } catch (e) {
    console.log(`Could not load ${file}`);
    return null;
  }
}).filter(Boolean);

console.log('🎯 CRASH MULTIPLIER PREDICTION ANALYSIS');
console.log('=======================================');
console.log(`Analyzing ${games.length} complete games`);

// Crash multiplier calculation function from the site
function calculateCrashMultiplier(serverSeed, clientSeed) {
  const saltedSeed = createHmac("sha256", serverSeed).update(clientSeed).digest("hex");
  
  if (isDivisible(saltedSeed, 20)) return 1.00;
  
  const h = parseInt(saltedSeed.slice(0, 13), 16);
  const e = Math.pow(2, 52);
  const crashPoint = Math.floor((100 * e - h) / (e - h)) / 100;
  
  return crashPoint;
}

function isDivisible(hash, mod) {
  return BigInt("0x" + hash) % BigInt(mod) === 0n;
}

// Analyze each game
const gameAnalysis = games.map(game => {
  const createStage = game.stages.CreateCrashRound;
  const instruction = createStage.transaction.transaction.message.instructions.find(
    inst => inst.programId === '6YVBrao9DSLVGk7QsXxQVPbs4XyHVtHw9oSTTXw1otEW'
  );
  
  const buffer = Buffer.from(instruction.data, 'base64');
  const data = buffer.slice(8);
  
  const serverSeed = game.api_snapshots.EndCrashRound?.serverSeed;
  const clientSeed = game.api_snapshots.EndCrashRound?.clientSeed;
  const actualMultiplier = game.api_snapshots.EndCrashRound?.crashMultiplier;
  
  // Calculate expected multiplier using the algorithm
  let calculatedMultiplier = null;
  let saltedSeed = null;
  
  if (serverSeed && clientSeed) {
    calculatedMultiplier = calculateCrashMultiplier(serverSeed, clientSeed);
    saltedSeed = createHmac("sha256", serverSeed).update(clientSeed).digest("hex");
  }
  
  return {
    gameId: game.game_info.custom_id,
    serverSeed,
    clientSeed,
    actualMultiplier,
    calculatedMultiplier,
    saltedSeed,
    
    // Instruction data values
    constant: data.readUInt32LE(0),
    variable1: data.readUInt32LE(4),
    variable2: data.readUInt32LE(8), // This is the d04d4d9d value you're interested in
    lastByte: data[12],
    
    // Hex values
    constantHex: data.slice(0, 4).toString('hex'),
    variable1Hex: data.slice(4, 8).toString('hex'),
    variable2Hex: data.slice(8, 12).toString('hex'), // d04d4d9d
    lastByteHex: data[12].toString(16).padStart(2, '0'),
    
    // Full variable part
    variablePart: data.slice(4).toString('hex')
  };
});

console.log('\n📊 MULTIPLIER VERIFICATION:');
console.log('===========================');

gameAnalysis.forEach((game, i) => {
  console.log(`\nGame ${i+1} (${game.gameId}):`);
  console.log(`  Actual Multiplier: ${game.actualMultiplier}`);
  console.log(`  Calculated Multiplier: ${game.calculatedMultiplier}`);
  console.log(`  Match: ${Math.abs(game.actualMultiplier - game.calculatedMultiplier) < 0.01 ? '✅ YES' : '❌ NO'}`);
  
  if (game.saltedSeed) {
    console.log(`  Salted Seed: ${game.saltedSeed.substring(0, 32)}...`);
    console.log(`  First 13 hex chars: ${game.saltedSeed.slice(0, 13)}`);
    console.log(`  As integer: ${parseInt(game.saltedSeed.slice(0, 13), 16)}`);
  }
});

console.log('\n🔍 INSTRUCTION DATA vs MULTIPLIER CORRELATION:');
console.log('==============================================');

gameAnalysis.forEach((game, i) => {
  console.log(`\nGame ${i+1} (${game.gameId}):`);
  console.log(`  Crash Multiplier: ${game.actualMultiplier}`);
  console.log(`  Variable2 (d04d4d9d type): ${game.variable2} (0x${game.variable2Hex})`);
  console.log(`  Variable1: ${game.variable1} (0x${game.variable1Hex})`);
  console.log(`  LastByte: ${game.lastByte} (0x${game.lastByteHex})`);
  
  // Check direct correlations
  const multiplierInt = Math.floor(game.actualMultiplier * 100); // Convert 3.72 to 372
  const multiplierInt1000 = Math.floor(game.actualMultiplier * 1000); // Convert 3.72 to 3720
  
  console.log(`  Multiplier as int (*100): ${multiplierInt}`);
  console.log(`  Multiplier as int (*1000): ${multiplierInt1000}`);
  
  // Check if any instruction values match multiplier representations
  if (game.variable2 === multiplierInt || game.variable2 === multiplierInt1000) {
    console.log(`  🎉 DIRECT MATCH! Variable2 matches multiplier!`);
  }
  
  if (game.variable1 === multiplierInt || game.variable1 === multiplierInt1000) {
    console.log(`  🎉 DIRECT MATCH! Variable1 matches multiplier!`);
  }
  
  // Check ratios
  const ratio1 = game.variable2 / game.actualMultiplier;
  const ratio2 = game.variable1 / game.actualMultiplier;
  
  console.log(`  Variable2 / Multiplier: ${ratio1}`);
  console.log(`  Variable1 / Multiplier: ${ratio2}`);
  
  // Check if the salted seed relates to instruction data
  if (game.saltedSeed) {
    const saltedSeedInt = parseInt(game.saltedSeed.slice(0, 13), 16);
    console.log(`  Salted seed (first 13): ${saltedSeedInt}`);
    
    // Check if instruction data could generate this
    if (game.variable2.toString(16) === game.saltedSeed.slice(0, 8)) {
      console.log(`  🎯 Variable2 hex matches start of salted seed!`);
    }
  }
});

console.log('\n🧮 MATHEMATICAL RELATIONSHIP ANALYSIS:');
console.log('=====================================');

gameAnalysis.forEach((game, i) => {
  console.log(`\nGame ${i+1} (${game.gameId}):`);
  
  // Try to reverse engineer the multiplier calculation
  const multiplier = game.actualMultiplier;
  
  // Check if we can derive the salted seed from instruction data
  if (game.saltedSeed) {
    const saltedSeedFirst13 = game.saltedSeed.slice(0, 13);
    const h = parseInt(saltedSeedFirst13, 16);
    const e = Math.pow(2, 52);
    
    console.log(`  Salted seed (13 chars): ${saltedSeedFirst13}`);
    console.log(`  h value: ${h}`);
    console.log(`  Expected multiplier: ${Math.floor((100 * e - h) / (e - h)) / 100}`);
    
    // Try to see if instruction data can generate h
    const attempts = [
      { name: 'variable2', value: game.variable2 },
      { name: 'variable1', value: game.variable1 },
      { name: 'variable1 + variable2', value: game.variable1 + game.variable2 },
      { name: 'variable1 XOR variable2', value: game.variable1 ^ game.variable2 },
      { name: 'variable2 << 16', value: game.variable2 << 16 },
      { name: 'variable1 << 16', value: game.variable1 << 16 }
    ];
    
    attempts.forEach(attempt => {
      const attemptHex = attempt.value.toString(16).padStart(13, '0').slice(0, 13);
      if (attemptHex === saltedSeedFirst13) {
        console.log(`  🎉 BREAKTHROUGH! ${attempt.name} generates the h value!`);
      }
    });
  }
});

console.log('\n🎯 PREDICTION ATTEMPT:');
console.log('======================');

// Try to predict multiplier using only instruction data
gameAnalysis.forEach((game, i) => {
  console.log(`\nGame ${i+1} (${game.gameId}):`);
  console.log(`  Actual Multiplier: ${game.actualMultiplier}`);
  
  // Attempt 1: Use variable2 as the h value directly
  const e = Math.pow(2, 52);
  const h1 = game.variable2;
  const predicted1 = Math.floor((100 * e - h1) / (e - h1)) / 100;
  console.log(`  Prediction 1 (using variable2 as h): ${predicted1}`);
  
  // Attempt 2: Use variable1 as the h value
  const h2 = game.variable1;
  const predicted2 = Math.floor((100 * e - h2) / (e - h2)) / 100;
  console.log(`  Prediction 2 (using variable1 as h): ${predicted2}`);
  
  // Attempt 3: Combine values
  const h3 = (game.variable1 + game.variable2) % Math.pow(2, 52);
  const predicted3 = Math.floor((100 * e - h3) / (e - h3)) / 100;
  console.log(`  Prediction 3 (using combined values): ${predicted3}`);
  
  // Check accuracy
  const accuracy1 = Math.abs(predicted1 - game.actualMultiplier);
  const accuracy2 = Math.abs(predicted2 - game.actualMultiplier);
  const accuracy3 = Math.abs(predicted3 - game.actualMultiplier);
  
  console.log(`  Accuracy 1: ${accuracy1.toFixed(4)} (${accuracy1 < 0.01 ? '✅ MATCH!' : '❌'})`);
  console.log(`  Accuracy 2: ${accuracy2.toFixed(4)} (${accuracy2 < 0.01 ? '✅ MATCH!' : '❌'})`);
  console.log(`  Accuracy 3: ${accuracy3.toFixed(4)} (${accuracy3 < 0.01 ? '✅ MATCH!' : '❌'})`);
});

console.log('\n📋 SUMMARY:');
console.log('===========');
console.log('🎯 Goal: Predict crash multiplier from CreateCrashRound instruction data');
console.log('📊 Algorithm: HMAC-SHA256(serverSeed, clientSeed) → h value → multiplier');
console.log('🔍 Focus: Can instruction data (especially variable2: d04d4d9d) predict the outcome?');
console.log('');
console.log('Key findings will show if any instruction values correlate with:');
console.log('1. The final crash multiplier');
console.log('2. The h value used in the calculation');
console.log('3. Parts of the salted seed');

// Save analysis
const predictionAnalysis = {
  goal: 'Predict crash multiplier from instruction data before game starts',
  algorithm: 'HMAC-SHA256(serverSeed, clientSeed) -> h value -> crash point',
  games: gameAnalysis,
  analysis_timestamp: new Date().toISOString()
};

fs.writeFileSync('./multiplier-prediction-analysis.json', JSON.stringify(predictionAnalysis, null, 2));
console.log('\n💾 Analysis saved to multiplier-prediction-analysis.json');
